{"name": "wyre-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,md,json}'", "openapi": "openapi-generator-cli generate -i swagger/swagger.json -g typescript-axios -o src/api --additional-properties=es6=true,supportsES6=true,modelPropertyNaming=camelCase,withInterfaces=true,withSeparateModelsAndApi=true,modelPackage=models,apiPackage=controllers"}, "dependencies": {"@syncfusion/ej2-base": "29.1.33", "@syncfusion/ej2-react-pdfviewer": "29.1.33", "@tanstack/react-query": "^5.68.0", "antd": "^5.24.2", "axios": "^1.8.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.0.375", "react": "19.0.0", "react-csv": "^2.2.2", "react-dom": "19.0.0", "react-icons": "^5.5.0", "react-router": "^7.2.0", "react-router-dom": "^7.2.0", "styled-components": "^6.1.15", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@openapitools/openapi-generator-cli": "^2.17.0", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.2", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite": "^6.2.0"}}