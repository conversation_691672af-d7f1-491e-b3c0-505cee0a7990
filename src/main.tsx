import { ConfigProvider, App } from 'antd';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { AppRoutes } from './routes/AppRoutes';
import './index.css';
import { ErrorBoundary } from './modules/error/ErrorBoundary';
import { themeTokens } from './theme/tokens';

createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ConfigProvider
      theme={{
        components: {
          Menu: {
            itemSelectedColor: themeTokens.textLight,
            itemSelectedBg: themeTokens.menuItemSelectedBg,
            itemHoverBg: themeTokens.menuItemHoverBg,
            itemHoverColor: themeTokens.textLight,
            itemColor: themeTokens.textLight,
            itemBg: themeTokens.siderBg,
            fontSize: 16
          },
          Button: {
            fontFamily: 'Inter',
            colorPrimary: themeTokens.primaryColor,
            colorPrimaryHover: themeTokens.primaryColor,
            colorPrimaryActive: themeTokens.primaryColor,
            contentFontSize: 15,
            fontWeight: 500,
            borderRadius: 3,
            controlHeight: 40
          },
          Layout: {
            siderBg: themeTokens.siderBg,
            headerBg: themeTokens.textLight,
            triggerBg: themeTokens.siderBg
          },
          Table: {
            headerBg: themeTokens.tableHeaderBg,
            headerColor: themeTokens.textLight,
            colorPrimary: themeTokens.textLight,
            filterDropdownBg: themeTokens.textLight,
            filterDropdownMenuBg: themeTokens.textLight,
            headerSortActiveBg: themeTokens.tableHeaderBg
          },
          Upload: {
            controlHeightLG: 60,
            paddingXS: 0
          },
          Modal: {
            titleFontSize: 30,
            titleColor: themeTokens.textDark
          },
          Form: {
            labelColor: themeTokens.textGray,
            itemMarginBottom: 12,
            labelFontSize: 16
          },
          DatePicker: {
            colorIcon: themeTokens.textGray,
            colorBorderBg: themeTokens.textGray
          },
          Select: {
            controlHeight: 40
          }
        }
      }}
    >
      <App>
        <BrowserRouter>
          <ErrorBoundary>
            <AppRoutes />
          </ErrorBoundary>
        </BrowserRouter>
      </App>
    </ConfigProvider>
  </React.StrictMode>
);
