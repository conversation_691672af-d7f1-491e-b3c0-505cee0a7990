import { FileExcelOutlined, FileTextOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Button, MenuProps, message } from 'antd';
import { CSVLink } from 'react-csv';
import * as XLSX from 'xlsx';

type ExportFileType = {
  data: any;
  columns: { label: string; key: string }[];
  filename: string;
};

const ExportFile: React.FC<ExportFileType> = ({ data, columns, filename }) => {
  const handleExcelExport = () => {
    const filteredData = data.map((row: Record<string, unknown>) =>
      columns.reduce(
        (obj, col) => {
          obj[col.key] = row[col.key];
          return obj;
        },
        {} as Record<string, unknown>
      )
    );
    // Create a worksheet with headers first
    const headers = columns.map(col => col.label);
    const worksheet = XLSX.utils.aoa_to_sheet([headers]);

    XLSX.utils.sheet_add_json(worksheet, filteredData, {
      skipHeader: true,
      origin: 'A2'
    });

    worksheet['!cols'] = columns.map(col => ({ wch: col.label.length + 2 }));
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    XLSX.writeFile(workbook, `${filename}.xlsx`);
  };

  const items: MenuProps['items'] = [
    {
      key: 'csv',
      label: (
        <div>
          <CSVLink data={data || []} headers={columns} filename={`${filename}.csv`} uFEFF={true}>
            CSV
          </CSVLink>
        </div>
      ),
      icon: <FileTextOutlined />
    },
    {
      key: 'xlsx',
      label: 'XLSX',
      icon: <FileExcelOutlined />,
      onClick: handleExcelExport
    }
  ];

  return (
    <Dropdown menu={{ items }} trigger={['click']}>
      <Button type='primary' icon={<DownOutlined size={20} />} iconPosition='end'>
        Export
      </Button>
    </Dropdown>
  );
};

export default ExportFile;
