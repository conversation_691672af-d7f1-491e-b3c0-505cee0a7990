import { FilterFilled, SearchOutlined } from '@ant-design/icons';
import { Input, Button, Space, InputRef } from 'antd';
import { ColumnType } from 'antd/es/table';
import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

interface ColumnFilterDropdownProps {
  dataIndex: string;
  setSelectedKeys: (keys: React.Key[]) => void;
  selectedKeys: React.Key[];
  confirm: () => void;
  clearFilters?: () => void;
  close: () => void;
}

const DropdownContainer = styled.div`
  padding: 8px;
`;

const StyledSearchInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const StyledButton = styled(Button)`
  width: 90px;
`;

const ColumnFilterDropdown: React.FC<ColumnFilterDropdownProps> = ({
  dataIndex,
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  close
}) => {
  const inputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  }, []);

  return (
    <DropdownContainer>
      <StyledSearchInput
        ref={inputRef}
        placeholder={`Search ${dataIndex}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={confirm}
      />
      <Space>
        <StyledButton
          type='primary'
          onClick={() => {
            confirm();
            close();
          }}
          icon={<SearchOutlined />}
          size='small'
        >
          Search
        </StyledButton>
        <StyledButton
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            close();
          }}
          size='small'
        >
          Reset
        </StyledButton>
      </Space>
    </DropdownContainer>
  );
};

const ColumnFilter = (dataIndex: any): ColumnType<any> => ({
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
    <ColumnFilterDropdown
      dataIndex={String(dataIndex)}
      setSelectedKeys={setSelectedKeys}
      selectedKeys={selectedKeys}
      confirm={confirm}
      clearFilters={clearFilters}
      close={close}
    />
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value, record) => {
    const recordValue = record[dataIndex];
    return recordValue
      ? recordValue
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase())
      : false;
  },
  onFilterDropdownOpenChange: visible => {
    if (visible) {
      setTimeout(() => {}, 100);
    }
  }
});

export default ColumnFilter;
