import { useQuery } from '@tanstack/react-query';
import { Flex, Layout, Menu } from 'antd';
import React, { ReactNode, useEffect, useMemo } from 'react';
import { FaUsers, FaCog } from 'react-icons/fa';
import { FaBuildingShield } from 'react-icons/fa6';
import { HiOutlineChevronRight } from 'react-icons/hi';
import { HiOutlineChevronLeft } from 'react-icons/hi2';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { bidItemAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CustomIcon from './customIcon';
import documentIcon from '../../assets/images/document.svg';
import enterpriseIcon from '../../assets/images/enterprise.svg';
import Logo from '../../assets/images/logo.svg';
import projectIcon from '../../assets/images/project.svg';
import scopeIcon from '../../assets/images/scope.svg';
import WyreAIIcon from '../../assets/images/wyreAIIcon.png';
import ScopeSubNavigation from '../scopes/scopeSubNavigation';
import useScopesStore from '../scopes/store/useScopesStore';
import { appRoutes, queryKeys } from '../utils/constant';
import {
  UserProjectPermission,
  UserPermission,
  useUserPermittedRoutes,
  useWyreAIAdminAccess
} from '../utils/permissions';
import { getPathName } from '../utils/util';

const { Sider } = Layout;

type MenuItem = {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  isSubMenu?: boolean;
  requiredPermissions?: Array<UserPermission | UserProjectPermission>;
};

const StyledSider = styled(Sider)`
  position: relative;
`;

const CollapseButton = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: ${themeTokens.collapseIconBg};
  border: 1px solid ${themeTokens.collapseIconBorder};
  top: 64px;
  right: -17px;
  z-index: 10;
  border-radius: 50%;
  height: 35px;
  cursor: pointer;
  && {
    width: 35px;
  }
`;

const StyledMenu = styled(Menu)<{ isSideMenuOpen?: boolean }>`
  padding: ${({ isSideMenuOpen }) => (isSideMenuOpen ? '0 32px' : '0')};
  font-size: 18px;
`;

const FlexContainer = styled(Flex)`
  padding: 10px 0 0 0;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 0 32px;
`;

const Title = styled.div`
  font-size: 21px;
  font-weight: 500;
  color: ${themeTokens.textLight};
`;

const Copyright = styled.div`
  position: absolute;
  bottom: 50px;
  color: ${themeTokens.textLight};
  left: 40px;
`;

const MenuIcon = styled.img`
  height: 18px;
`;

const Sidebar: React.FC = () => {
  const { projectId, scopeId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    selectedProjectId,
    setSelectedProjectId,
    isScopeSubMenuOpen,
    setScopeSubMenuOpen,
    isSideMenuOpen,
    setIsSideMenuOpen,
    selectedVersion
  } = useGlobalStore();

  const { isDocumentView, setIsDocumentView, setSelectedThumbnailInfo } = useScopesStore();

  const { data: fetchedBidItems } = useQuery({
    queryKey: [queryKeys.allBidItems, scopeId, selectedVersion],
    queryFn: () => bidItemAPI.getBidItemsByScopeId(Number(scopeId), 0, 1000),
    select: res => res.data,
    retry: false,
    enabled: !!scopeId
  });

  useEffect(() => {
    if (projectId) {
      setSelectedProjectId(projectId);
    }
  }, [projectId, setSelectedProjectId]);

  // Open scope submenu when on a scope page
  useEffect(() => {
    if (location.pathname.includes(`/${appRoutes.scopes}`)) {
      setScopeSubMenuOpen(true);
    }
  }, [location.pathname, setScopeSubMenuOpen]);

  const { hasAccess: hasWyreAIAdminAccess } = useWyreAIAdminAccess();

  const routes: MenuItem[] = [
    {
      key: `/${appRoutes.projects}`,
      label: 'Projects',
      icon: <MenuIcon src={projectIcon} />
    },
    {
      key: `/${appRoutes.user}`,
      label: 'Users',
      icon: <CustomIcon Icon={FaUsers} />,
      requiredPermissions: [UserPermission.CREATE_USERS]
    },
    {
      key: `/${appRoutes.company}`,
      label: 'Companies',
      icon: <CustomIcon Icon={FaBuildingShield} />,
      requiredPermissions: [UserPermission.CREATE_COMPANY]
    },
    {
      key: `/${appRoutes.enterprise}`,
      icon: <MenuIcon src={enterpriseIcon} />,
      label: 'Enterprise'
    },
    ...(hasWyreAIAdminAccess
      ? [
          {
            key: `/${appRoutes.wyreAIAdmin}`,
            label: 'Wyre AI Admin',
            icon: <CustomIcon Icon={FaCog} />
          }
        ]
      : [])
  ];

  const permittedRoutes = useUserPermittedRoutes(routes);

  const projectRoutes: MenuItem[] = useMemo(
    () =>
      selectedProjectId
        ? [
            {
              key: `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.documents}`,
              label: 'Documents',
              icon: <MenuIcon src={documentIcon} />
            },
            {
              key: scopeId
                ? `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}/${scopeId}`
                : `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}`,
              label: 'Scopes',
              icon: <MenuIcon src={scopeIcon} />,
              isSubMenu: true
            }
            // Will be added in future
            // {
            //   key: `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.bidSheets}`,
            //   label: 'Bid Sheets',
            //   icon: <CustomIcon Icon={HiDocumentText} />
            // },
            // {
            //   key: `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.dashboard}`,
            //   label: 'Dashboard',
            //   icon: <CustomIcon Icon={RiDashboardFill} />
            // }
          ]
        : [],
    [scopeId, selectedProjectId]
  );
  const getNavRoutes = () => {
    const routes = [...permittedRoutes];
    if (selectedProjectId) {
      routes.splice(1, 0, ...projectRoutes);
    }
    return routes;
  };

  // Get the active key based on the current path
  const getActiveKey = () => {
    const path = location.pathname;
    const pathSegments = path.split('/').filter(Boolean);

    // Handle project-specific routes
    if (pathSegments.length >= 2 && pathSegments[0] === appRoutes.projects && selectedProjectId) {
      // For project details page
      if (pathSegments.length === 2) {
        return `/${appRoutes.projects}`;
      }

      // For project sections
      if (pathSegments.length >= 3) {
        const section = pathSegments[2];

        if (section === appRoutes.documents) {
          return `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.documents}`;
        }
        if (section === appRoutes.scopes) {
          return scopeId
            ? `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}/${scopeId}`
            : `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.scopes}`;
        }
        if (section === appRoutes.bidSheets) {
          return `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.bidSheets}`;
        }
        if (section === appRoutes.dashboard) {
          return `/${appRoutes.projects}/${selectedProjectId}/${appRoutes.dashboard}`;
        }
      }
    }

    // For main sections, use getPathName
    return `/${getPathName(path)}`;
  };

  const onClickMenuHandler = (event: { key: string }) => {
    if (event?.key !== location.pathname) navigate(event?.key);
    if (location.pathname.includes(appRoutes.scopes)) {
      setIsDocumentView(false);
      setScopeSubMenuOpen(true);
    }
  };

  return (
    <>
      <StyledSider
        collapsible
        collapsed={isSideMenuOpen}
        onCollapse={setIsSideMenuOpen}
        width={255}
        trigger={null}
      >
        <CollapseButton onClick={() => setIsSideMenuOpen(!isSideMenuOpen)}>
          {isSideMenuOpen ? (
            <HiOutlineChevronRight size='18px' color='white' />
          ) : (
            <HiOutlineChevronLeft size='18px' color='white' />
          )}
        </CollapseButton>
        <FlexContainer gap={20} vertical>
          {!isSideMenuOpen && (
            <Container>
              <Flex align='flex-end' vertical>
                <img width={188} src={Logo} alt='Wyre AI' />
              </Flex>
              {!isSideMenuOpen && <Copyright>© Wyre AI ScopeBuilder</Copyright>}
            </Container>
          )}
          {isSideMenuOpen && (
            <Flex justify='center'>
              <img width={40} src={WyreAIIcon} alt='Wyre AI' />
            </Flex>
          )}
          <StyledMenu
            onClick={onClickMenuHandler}
            selectedKeys={[getActiveKey()]}
            items={getNavRoutes()}
            isSideMenuOpen={!isSideMenuOpen}
          />
        </FlexContainer>
        {!isScopeSubMenuOpen && !isSideMenuOpen ? (
          <HiOutlineChevronRight
            onClick={() => setScopeSubMenuOpen(!isScopeSubMenuOpen)}
            size={22}
            color={themeTokens.textLight}
            cursor='pointer'
            style={{ position: 'absolute', right: 36, top: 190 }}
          />
        ) : null}
      </StyledSider>
      {scopeId && (
        <ScopeSubNavigation isDocumentView={isDocumentView} bidItems={fetchedBidItems?.content} />
      )}
    </>
  );
};

export default Sidebar;
export type { MenuItem };
