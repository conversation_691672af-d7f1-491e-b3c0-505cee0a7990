import { useQuery } from '@tanstack/react-query';
import { ReactNode, useEffect } from 'react';
import { Navigate } from 'react-router';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { queryKeys } from '../utils/constant';
import { clearStorage } from '../utils/storage';

interface AuthGuardProps {
  children: ReactNode;
}

const AuthGuard = (props: AuthGuardProps) => {
  const { children } = props;
  const { setIsAuthenticated, setCurrentUser } = useGlobalStore();
  const { data, isSuccess, isLoading } = useQuery<{ data: UserDTO }>({
    queryKey: [queryKeys.userDetails],
    queryFn: () => userAPI.getMyDetails()
  });

  useEffect(() => {
    if (isSuccess && data?.data) {
      setIsAuthenticated(isSuccess);
      setCurrentUser(data.data);
    }
  }, [isSuccess, data, setIsAuthenticated, setCurrentUser]);

  if (isLoading) return <></>;

  if (!isLoading && !isSuccess) {
    clearStorage();
    return <Navigate to='/' />;
  }

  return <>{children}</>;
};

export default AuthGuard;
