import { useQuery } from '@tanstack/react-query';
import { Switch, Flex } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  BidItemSheetDTO,
  DocumentWithPresignedUrlDTO,
  DocumentWithPresignedUrlDTOInputDocumentTypeEnum
} from 'src/api';
import { bidItemAPI, documentAPI, projectAPI, scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import DocumentThumbnailsView from './documentThumbnailsView';
import ScopeTableView from './scopeTableView';
import SearchDropdown from '../common/searchDropdown';
import { queryKeys } from '../utils/constant';
import useScopesStore from './store/useScopesStore';
import { SpecThumbnailInfo, ThumbnailInfo, getThumbnailS3Url } from '../utils/thumbnailInfo';

const Container = styled(Flex)`
  height: 100%;
  justify-content: center;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0 0;
  width: 90%;
`;

const HeaderText = styled.div`
  font-weight: 500;
  font-size: 25px;
  line-height: 28px;
`;

const DocumentViewText = styled.div`
  font-weight: 400;
  font-size: 16px;
`;

const ScopeInfo = () => {
  const [isThumbnailsOpen, setIsThumbnailsOpen] = useState<boolean>(false);
  const [isThumbnailsDrawerMinimised, setIsThumbnailsDrawerMinimised] = useState<boolean>(false);
  const {
    isDocumentView,
    setIsDocumentView,
    setDrawingsThumbnails,
    setSpecificationsThumbnails,
    setIsCreatingThumbnails,
    selectedThumbnailInfo,
    setSelectedThumbnailInfo
  } = useScopesStore();
  const { selectedProjectId, currentUser, selectedVersion } = useGlobalStore();
  const { scopeId } = useParams();
  const navigate = useNavigate();
  const scopeRef = useRef<string>(null);

  const { data: scopesList } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId, selectedThumbnailInfo?.sheet],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        undefined,
        0,
        1000,
        ['division,asc'],
        selectedThumbnailInfo?.sheet.sheetId
      ),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  const { data: scopeInfo } = useQuery({
    queryKey: [queryKeys.scopeInfo, scopeId],
    queryFn: () => scopeAPI.getScopeById(Number(scopeId)),
    select: res => res.data,

    enabled: !!scopeId
  });

  const { data: fetchedBidItems } = useQuery({
    queryKey: [queryKeys.allBidItems, scopeId],
    queryFn: () => bidItemAPI.getBidItemsByScopeId(Number(scopeId)),
    select: res => res.data,
    retry: false,
    enabled: !!scopeId
  });
  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: !!selectedProjectId,
    select: res => res.data
  });

  useEffect(() => {
    fetchImages();
  }, [fetchedBidItems, documentsData]);

  const fetchImages = async () => {
    if (!fetchedBidItems || !documentsData) return;
    // listening to scope id changes and changing thumbnails
    if (scopeRef && scopeRef.current === scopeId) return;
    scopeRef.current = scopeId || null;
    setIsCreatingThumbnails(true);
    try {
      const combinedObject: {
        [key: number]: { document: DocumentWithPresignedUrlDTO | null; sheets: BidItemSheetDTO[] };
      } = {};
      fetchedBidItems.content?.forEach(bidItem => {
        bidItem?.bidItemSheets?.forEach(sheet => {
          const documentId = sheet.documentId || 0;
          if (!combinedObject[documentId]) {
            combinedObject[documentId] = { document: null, sheets: [] };
          }
          if (combinedObject[documentId].sheets.find(s => s.sheetId === sheet.sheetId)) return;
          combinedObject[documentId].sheets.push(sheet);
        });
      });
      Object.keys(combinedObject).forEach(documentIdStr => {
        const documentId = Number(documentIdStr);
        const document = documentsData?.documents?.find(doc => doc.id === documentId);
        if (document) {
          combinedObject[documentId].document = document;
        }
      });

      const thumbnails: Array<ThumbnailInfo> = Object.keys(combinedObject)
        .map(documentIdStr => {
          const document = combinedObject[Number(documentIdStr)].document;
          const sheets = combinedObject[Number(documentIdStr)].sheets;
          const res: ThumbnailInfo[] = sheets.map(sheet => ({
            document: document,
            sheet: sheet,
            thumbnail: getThumbnailS3Url(
              currentUser?.companyId || 0,
              Number(selectedProjectId),
              Number(documentIdStr),
              sheet.pageNumber || 0
            )
          }));
          return res;
        })
        .flat();

      let specificationThumbnails: SpecThumbnailInfo[] = [];

      fetchedBidItems.content?.forEach(bidItem => {
        specificationThumbnails =
          bidItem?.bidItemSpecificationDTOList?.map(specification => {
            const document = documentsData?.documents?.find(
              doc => doc.id === specification.documentId
            );
            return {
              // const documentId = specification.documentId || 0;
              // if (!combinedObject[documentId]) {
              //   [documentId] = { document: null, sheets: [], specifications: [] };
              // }
              // if (
              //   combinedObject[documentId].specifications.find(
              //     s => s.specificationId === specification.specificationId
              //   )
              // )
              //   return;
              // combinedObject[documentId].specifications.push(specification);
              // const res: ThumbnailInfo[] = sheets.map(sheet => ({
              document,
              specification: specification,
              thumbnail: getThumbnailS3Url(
                currentUser?.companyId || 0,
                Number(selectedProjectId),
                specification.documentId,
                specification.pageNumber || 0
              )
              // }));

              // return res;
            };
          }) || [];
      });

      const drawingThumbnails = thumbnails.filter(
        thumbnail =>
          thumbnail.document?.inputDocumentType ===
          DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing
      );
      // const specificationThumbnails = thumbnails.filter(
      //   thumbnail =>
      //     thumbnail.document?.inputDocumentType ===
      //     DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Specification
      // );
      setDrawingsThumbnails(drawingThumbnails);
      if (drawingThumbnails.length > 0) {
        setSelectedThumbnailInfo(drawingThumbnails[0]);
      } else if (specificationThumbnails.length > 0) {
        setSelectedThumbnailInfo(specificationThumbnails[0]);
      }
      setSpecificationsThumbnails(specificationThumbnails);
    } catch (error) {
      console.error('Failed to fetch thumbnails:', error);
    } finally {
      setIsCreatingThumbnails(false);
    }
  };

  return (
    <Container>
      <Content>
        {!isThumbnailsOpen && (
          <Flex align='center' justify='space-between'>
            {!isDocumentView ? (
              <HeaderText>{scopeInfo?.scopeNameWithSpecCode}</HeaderText>
            ) : (
              <Flex align='center' gap={16}>
                <HeaderText>Scope</HeaderText>
                <SearchDropdown
                  width='200px'
                  options={
                    scopesList?.content
                      ? scopesList.content.map(scope => ({
                          label: scope.scopeNameWithSpecCode || '',
                          value: String(scope.id)
                        }))
                      : []
                  }
                  value={scopeId}
                  onChange={(value: string) => navigate(`../${value}`)}
                />
              </Flex>
            )}
            <Flex align='center' gap={16}>
              <DocumentViewText>Document View</DocumentViewText>
              <Switch
                defaultChecked
                value={isDocumentView}
                onChange={value => setIsDocumentView(value)}
              />
            </Flex>
          </Flex>
        )}
        {!isDocumentView ? (
          <ScopeTableView fetchedBidItems={fetchedBidItems?.content} />
        ) : (
          <DocumentThumbnailsView
            isThumbnailsOpen={isThumbnailsOpen}
            setIsThumbnailsOpen={setIsThumbnailsOpen}
            isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised}
            setIsThumbnailsDrawerMinised={setIsThumbnailsDrawerMinimised}
          />
        )}
      </Content>
    </Container>
  );
};

export default ScopeInfo;
