import { useQuery } from '@tanstack/react-query';
import { Switch, Flex, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DocumentWithPresignedUrlDTOInputDocumentTypeEnum } from 'src/api';
import { bidItemAPI, openSearchAPI, projectAPI, scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import DocumentThumbnailsView from './documentThumbnailsView';
import ScopeTableView from './scopeTableView';
import SearchDropdown from '../common/searchDropdown';
import { queryKeys } from '../utils/constant';
import useScopesStore from './store/useScopesStore';
import { ThumbnailInfo, getThumbnailS3Url } from '../utils/thumbnailInfo';

const Container = styled(Flex)`
  height: 100%;
  justify-content: center;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0;
  width: 90%;
`;

const HeaderText = styled.div`
  font-weight: 500;
  font-size: 25px;
  line-height: 28px;
`;

const DocumentViewText = styled.div`
  font-weight: 400;
  font-size: 16px;
`;

const ScopeInfo = () => {
  const [isThumbnailsOpen, setIsThumbnailsOpen] = useState<boolean>(false);
  const [isThumbnailsDrawerMinimised, setIsThumbnailsDrawerMinimised] = useState<boolean>(false);
  const {
    isDocumentView,
    setIsDocumentView,
    setDrawingsThumbnails,
    setSpecificationsThumbnails,
    setIsCreatingThumbnails,
    selectedThumbnailInfo,
    setSelectedThumbnailInfo
  } = useScopesStore();
  const { selectedProjectId, currentUser, selectedVersion } = useGlobalStore();
  const { scopeId } = useParams();
  const navigate = useNavigate();
  const scopeRef = useRef<string>(null);

  const { data: scopesList } = useQuery({
    queryKey: [
      queryKeys.allScopes,
      selectedProjectId,
      selectedThumbnailInfo?.sheet,
      selectedVersion,
      selectedThumbnailInfo?.specification
    ],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        String(selectedVersion?.version),
        0,
        1000,
        ['division,asc'],
        selectedThumbnailInfo?.sheet?.sheetId,
        selectedThumbnailInfo?.specification?.specificationId
      ),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  const { data: scopeInfo } = useQuery({
    queryKey: [queryKeys.scopeInfo, scopeId],
    queryFn: () => scopeAPI.getScopeById(Number(scopeId)),
    select: res => res.data,

    enabled: !!scopeId
  });

  const { data: fetchedBidItems, isLoading: isFetchingBidItems } = useQuery({
    queryKey: [queryKeys.allBidItems, scopeId, selectedVersion],
    queryFn: () => bidItemAPI.getBidItemsByScopeId(Number(scopeId), 0, 1000),
    select: res => res.data,
    retry: false,
    enabled: !!scopeId
  });
  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, selectedProjectId],
    queryFn: () => projectAPI.getProjectWithDocuments(Number(selectedProjectId)),
    enabled: !!selectedProjectId,
    select: res => res.data
  });

  const { data: currentScopeDrawingsAndScecs, isFetching: isFetchingDrawingsAndSpecs } = useQuery({
    queryKey: [queryKeys.currentScopeDrawingsAndSpecs, selectedProjectId, scopeId],
    queryFn: () =>
      bidItemAPI.getSheetsAndSpecs(Number(selectedProjectId), scopeId, scopeId, 0, 1000),
    select: res => res.data,
    enabled: !!selectedProjectId && !!scopeId
  });

  useEffect(() => {
    if (isFetchingDrawingsAndSpecs) {
      setIsCreatingThumbnails(true);
      return;
    }
    const currentScopeDrawings = currentScopeDrawingsAndScecs?.bidItemSheets || [];
    const currentScopeSpecs = currentScopeDrawingsAndScecs?.bidItemSpecificationDTOList || [];
    let drawingThumbnails: ThumbnailInfo[] = [];
    let specificationThumbnails: ThumbnailInfo[] = [];
    if (currentScopeDrawings) {
      drawingThumbnails = currentScopeDrawings?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          sheet: item,
          thumbnail
        };
      });
      setDrawingsThumbnails(drawingThumbnails);
    }
    if (currentScopeSpecs) {
      specificationThumbnails = currentScopeSpecs?.map(item => {
        const thumbnail = getThumbnailS3Url(
          currentUser?.companyId || 0,
          Number(selectedProjectId),
          Number(item.documentId),
          Number(item.pageNumber) || 0
        );
        const document = documentsData?.documents?.find(doc => doc.id === item.documentId) || null;
        return {
          document,
          thumbnail,
          specification: item
        };
      });
      setSpecificationsThumbnails(specificationThumbnails);
    }
    const setFallbackThumbnail = () => {
      if (drawingThumbnails.length > 0) setSelectedThumbnailInfo(drawingThumbnails[0]);
      else if (specificationThumbnails.length > 0)
        setSelectedThumbnailInfo(specificationThumbnails[0]);
      else setSelectedThumbnailInfo(null);
    };

    if (!isDocumentView || selectedThumbnailInfo === null) {
      setFallbackThumbnail();
    } else {
      const docType = selectedThumbnailInfo?.document?.inputDocumentType;
      if (docType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
        const found = drawingThumbnails.find(
          thumbnail => thumbnail.sheet?.sheetId === selectedThumbnailInfo?.sheet?.sheetId
        );
        if (!found) setFallbackThumbnail();
      } else {
        const found = specificationThumbnails.find(
          thumbnail =>
            thumbnail.specification?.specificationId ===
            selectedThumbnailInfo?.specification?.specificationId
        );
        if (!found) setFallbackThumbnail();
      }
    }
    setIsCreatingThumbnails(false);
  }, [currentScopeDrawingsAndScecs, isFetchingDrawingsAndSpecs]);

  useEffect(() => {
    if (!isDocumentView) {
      if (isThumbnailsOpen) setIsThumbnailsOpen(false);
      if (isThumbnailsDrawerMinimised) setIsThumbnailsDrawerMinimised(false);
    }
  }, [isDocumentView]);

  return (
    <Container>
      <Content>
        {!isThumbnailsOpen && (
          <Flex align='center' justify='space-between'>
            {!isDocumentView ? (
              <HeaderText>{scopeInfo?.scopeNameWithSpecCode || scopeInfo?.name}</HeaderText>
            ) : (
              <Flex align='center' gap={16}>
                <HeaderText>Scope</HeaderText>
                <SearchDropdown
                  width='200px'
                  options={
                    scopesList?.content
                      ? scopesList.content?.map(scope => ({
                          label: scope?.scopeNameWithSpecCode || scope.name || '',
                          value: String(scope.id)
                        }))
                      : []
                  }
                  value={scopeId}
                  onChange={(value: string) => navigate(`../${value}`)}
                />
              </Flex>
            )}
            <Flex align='center' gap={16}>
              <DocumentViewText>Document View</DocumentViewText>
              {isFetchingDrawingsAndSpecs ? (
                <Tooltip title='Loading thumbnails, please wait...'>
                  <Switch
                    defaultChecked
                    value={isDocumentView}
                    onChange={value => setIsDocumentView(value)}
                    disabled
                  />
                </Tooltip>
              ) : (
                <Switch
                  defaultChecked
                  value={isDocumentView}
                  onChange={value => setIsDocumentView(value)}
                />
              )}
            </Flex>
          </Flex>
        )}
        {!isDocumentView ? (
          <ScopeTableView
            fetchedBidItems={fetchedBidItems?.content}
            isFetchingBidItems={isFetchingBidItems}
          />
        ) : (
          <DocumentThumbnailsView
            isThumbnailsOpen={isThumbnailsOpen}
            setIsThumbnailsOpen={setIsThumbnailsOpen}
            isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised}
            setIsThumbnailsDrawerMinised={setIsThumbnailsDrawerMinimised}
          />
        )}
      </Content>
    </Container>
  );
};

export default ScopeInfo;
