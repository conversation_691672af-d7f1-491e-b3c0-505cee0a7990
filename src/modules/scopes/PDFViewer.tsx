import {
  PdfViewerComponent,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  Print,
  TextSelection,
  Annotation,
  TextSearch,
  FormFields,
  FormDesigner,
  Inject,
  ExtractTextOption,
  AnnotBounds,
  PageOrganizer,
  AnnotationDataFormat
} from '@syncfusion/ej2-react-pdfviewer';

import './index.css';
import { useMutation, useQuery } from '@tanstack/react-query';
import { App } from 'antd';
import { PDFDocument } from 'pdf-lib';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { DocumentWithPresignedUrlDTOInputDocumentTypeEnum, NoteDTO } from 'src/api';
import { noteAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import AddBidItem from './addBidItem';
import AddBidInstructionsModal from './components/AddBidInstructionsModal';
import useScopesStore from './store/useScopesStore';
import { StorageKey, queryKeys } from '../utils/constant';
import { getItem } from '../utils/storage';

const Container = styled.div<{ isLoading: boolean }>`
  height: ${({ isLoading }) => (isLoading ? '0px' : '100%')};
  overflow: auto;
  width: 100%;
  display: flex;
  gap: 20px;
`;

enum CustomToolbarItems {
  add = 'add',
  previousPage = 'previous_page',
  nextPage = 'next_page',
  saveAnnotation = 'save_annotation'
}

type TextBounds = {
  left: number;
  right: number;
  top: number;
  bottom: number;
  width: number;
  height: number;
  pageIndex: number;
};

type TextSelectionProps = {
  name: string;
  textContent: string;
  textBounds: Array<TextBounds>;
  pageIndex: number;
};

const PDFViewer: React.FC<{
  isThumbnailsDrawerMinimised: boolean;
}> = ({ isThumbnailsDrawerMinimised }) => {
  const viewer = useRef<PdfViewerComponent>(null);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const {
    isScopeSubMenuOpen,
    isSideMenuOpen,
    setIsSideMenuOpen,
    setScopeSubMenuOpen,
    selectedProjectId
  } = useGlobalStore();
  const [extractedText, setExtractedText] = useState<Array<{ Bounds: AnnotBounds; Text: string }>>(
    []
  );
  const [activeAnnotation, setActiveAnnotation] = useState<any>();
  const [originalPageSize, setOriginalPageSize] = useState<any>();
  const [isAddInstructionsModalVisible, setIsAddInstructionsModalVisible] =
    useState<boolean>(false);
  const [selectedTextInfo, setSelectedTextInfo] = useState<TextSelectionProps | null>(null);
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const [isPageRendered, setIsPageRendered] = useState<boolean>(false);
  const [isAddBidContainerVisible, setIsAddBidContainerVisible] = useState<boolean>(false);
  const {
    selectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails,
    setSelectedThumbnailInfo,
    setIsLoadingDocument,
    isLoadingDocument
  } = useScopesStore();
  const { scopeId } = useParams();
  const { notification } = App.useApp();

  const {
    mutateAsync: createNotes,
    status: createNotesStatus,
    reset: resetCreateNotes
  } = useMutation({
    mutationFn: (data: NoteDTO) => noteAPI.createNote(data),
    onSuccess: () => {
      notification.success({ message: 'Saved document successfully' });
    }
  });

  const {
    mutateAsync: updateNotes,
    status: updateNotesStatus,
    reset: resetUpdateNotes
  } = useMutation({
    mutationFn: (props: { id: number; data: NoteDTO }) => noteAPI.updateNote(props.id, props.data),
    onSuccess: () => {
      notification.success({ message: 'Saved document successfully' });
    }
  });

  const {
    mutateAsync: deleteNotes,
    status: deleteNotesStatus,
    reset: resetDeleteNotes
  } = useMutation({
    mutationFn: (id: number) => noteAPI.deleteNote(id),
    onSuccess: () => {
      notification.success({ message: 'Saved document successfully' });
    }
  });

  const { data: notesData } = useQuery({
    queryKey: [
      queryKeys.getNotes,
      scopeId,
      selectedProjectId,
      selectedThumbnailInfo?.document?.id,
      selectedThumbnailInfo?.sheet.sheetId
    ],
    queryFn: () =>
      noteAPI.getNotes(
        undefined,
        Number(selectedProjectId),
        selectedThumbnailInfo?.document?.id,
        Number(scopeId),
        undefined,
        selectedThumbnailInfo?.sheet.sheetId
      ),
    select: res => res.data.content,
    enabled: !!(
      selectedProjectId &&
      selectedThumbnailInfo?.document?.id &&
      scopeId &&
      selectedThumbnailInfo.sheet.sheetId
    )
  });

  useEffect(() => {
    if (notesData && notesData.length > 0 && viewer.current && isPageRendered) {
      const note = notesData[0];
      if (note && note.annotationData) {
        try {
          const annotationData = note.annotationData;
          viewer?.current?.importAnnotation(JSON.parse(annotationData), AnnotationDataFormat.Json);
          setIsPageRendered(false);
        } catch (error) {
          console.error('Error importing annotations:', error);
        }
      }
    }
  }, [notesData, isPageRendered]);

  useEffect(() => {
    if (pdfUrl) {
      setTimeout(() => {
        setIsPdfReady(true);
      }, 100);
    } else {
      setIsPdfReady(false);
    }
  }, [pdfUrl]);

  async function extractAndCreateChildPDF() {
    if (!selectedThumbnailInfo?.document?.presignedUrl || !selectedThumbnailInfo?.sheet?.pageNumber)
      return;
    try {
      setIsLoadingDocument(true);
      const response = await fetch(selectedThumbnailInfo?.document?.presignedUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch the PDF');
      }
      const existingPdfBytes = await response.arrayBuffer();
      const parentPdfDoc = await PDFDocument.load(existingPdfBytes);
      const childPdfDoc = await PDFDocument.create();
      const [extractedPage] = await childPdfDoc.copyPages(parentPdfDoc, [
        selectedThumbnailInfo.sheet.pageNumber - 1
      ]);
      childPdfDoc.addPage(extractedPage);
      const childPdfBytes = await childPdfDoc.save();
      const blob = new Blob([childPdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      setPdfUrl(url);
    } catch (error) {
      console.error('Error generating the child PDF:', error);
    }
  }

  useEffect(() => {
    extractAndCreateChildPDF();
    if (isPageRendered) setIsPageRendered(false);
    if (selectedTextInfo) setSelectedTextInfo(null);
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [selectedThumbnailInfo]);

  useEffect(() => {
    if (viewer.current) {
      //Adding small delay to ensure viewer container updates only after animation of side bar is completed
      udpatePDFViewerContainer(500);
    }
  }, [isSideMenuOpen, isScopeSubMenuOpen, isThumbnailsDrawerMinimised]);

  const onAddAnnotation = async (e: any) => {
    setActiveAnnotation(e);
  };

  const extractAnnotationImage = async (e: any) => {
    const viewerRef = viewer?.current;
    if (!viewerRef || !selectedThumbnailInfo?.thumbnail) return;
    //Note: For not breaking pixels on image follow these steps: exportAsImage -> dimensions from pageInfo -> draw image on canvas with those dimensions -> use that for cropping
    const image = new Image();
    image.src = selectedThumbnailInfo.thumbnail;
    const coordinates = e.annotationBound;
    const { top, left, height, width } = coordinates;
    const pixelToPoints = 72 / 96;
    const sx = left * pixelToPoints;
    const sy = top * pixelToPoints;
    const sw = width * pixelToPoints;
    const sh = height * pixelToPoints;

    const croppedCanvas = document.createElement('canvas');
    croppedCanvas.width = sw;
    croppedCanvas.height = sh;

    const ctx = croppedCanvas.getContext('2d');
    if (!ctx) return;

    ctx.drawImage(image, sx, sy, sw, sh, 0, 0, sw, sh);

    const imageUrl = croppedCanvas.toDataURL('image/png');
  };

  const onExtractText = (e: any) => {
    const text = e.documentTextCollection[0][0].TextData;
    const originalPageSize = e.documentTextCollection[0][0].PageSize;
    setExtractedText(text);
    setOriginalPageSize(originalPageSize);
  };

  const onPageRendered = () => {
    setIsPageRendered(true);
  };

  const onDocumentLoad = () => {
    setIsLoadingDocument(false);
  };

  const extractTextOverArea = async () => {
    if (activeAnnotation && viewer.current) {
      const pageInfo = viewer.current.getPageInfo(0);
      if (pageInfo.height) {
        const scaleFactor = pageInfo?.height / originalPageSize.Height;
        const scaledBounds = {
          height: activeAnnotation.annotationBound.height * scaleFactor,
          left: activeAnnotation.annotationBound.left * scaleFactor,
          top: activeAnnotation.annotationBound.top * scaleFactor,
          width: activeAnnotation.annotationBound.width * scaleFactor
        };
        const filteredData = extractedText.filter(item => {
          if (
            item.Bounds.Left >= scaledBounds.left &&
            item.Bounds.Top >= scaledBounds.top &&
            item.Bounds.Right <= scaledBounds.left + scaledBounds.width &&
            item.Bounds.Bottom <= scaledBounds.top + scaledBounds.height
          )
            return item;
        });
        if (filteredData?.length > 0) {
          const extractedText = filteredData.map(itm => itm.Text).join('');
        }
      }
    }
  };

  const onBidItemAdded = async () => {
    if (viewer.current && selectedTextInfo) {
      const bounds = selectedTextInfo.textBounds.map(itm => ({
        width: itm.width,
        y: itm.top,
        x: itm.left,
        height: itm.height
      }));
      viewer.current.annotation.addAnnotation('Highlight', {
        bounds,
        pageNumber: selectedTextInfo?.pageIndex
      } as any);
      saveAnnotations();
    }
    setSelectedTextInfo(null);
  };

  const onPreviousPage = () => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== 0) {
        setSelectedThumbnailInfo(drawingsThumbnails[index - 1]);
      }
    } else {
      const length = specificationsThumbnails.length;
      const index = specificationsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== 0) {
        setSelectedThumbnailInfo(specificationsThumbnails[index - 1]);
      }
    }
  };

  const onNextPage = () => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== length - 1) {
        setSelectedThumbnailInfo(drawingsThumbnails[index + 1]);
      }
    } else {
      const length = specificationsThumbnails.length;
      const index = specificationsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== length - 1) {
        setSelectedThumbnailInfo(specificationsThumbnails[index + 1]);
      }
    }
  };

  const saveAnnotations = async () => {
    const annotations = await viewer.current?.exportAnnotationsAsObject();
    if (annotations) {
      if (
        selectedProjectId &&
        selectedThumbnailInfo?.document?.id &&
        selectedThumbnailInfo?.sheet?.sheetId
      ) {
        const data: NoteDTO = {
          scopeId: scopeId ? Number(scopeId) : undefined,
          projectId: Number(selectedProjectId),
          documentId: selectedThumbnailInfo.document.id,
          annotationData: JSON.stringify(annotations),
          sheetId: selectedThumbnailInfo?.sheet?.sheetId
        };
        try {
          if (notesData && notesData.length > 0) {
            const note = notesData[0];
            if (note && note.id) {
              await updateNotes({ id: note.id, data });
            } else {
              await createNotes(data);
            }
          } else {
            await createNotes(data);
          }
        } catch (error) {
          console.error(error);
        } finally {
          resetCreateNotes();
          resetUpdateNotes();
        }
      }
    } else {
      try {
        if (notesData && notesData.length > 0) {
          notesData.map((note: NoteDTO) => {
            if (note.id) deleteNotes(note.id);
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        resetDeleteNotes();
      }
    }
  };

  const udpatePDFViewerContainer = (delay?: number) => {
    setTimeout(() => {
      viewer?.current?.updateViewerContainer();
    }, delay || 100);
  };

  const onAddClick = () => {
    if (selectedTextInfo) {
      setIsAddBidContainerVisible(true);
      if (isSideMenuOpen && !isScopeSubMenuOpen) {
        udpatePDFViewerContainer();
      } else {
        setIsSideMenuOpen(true);
        setScopeSubMenuOpen(false);
      }
    } else {
      const isDontShowAgain = getItem(StorageKey.instructionsModalChecked);
      if (!isDontShowAgain) {
        setIsAddInstructionsModalVisible(true);
      } else {
        notification.error({ message: 'Please select text to continue' });
      }
    }
  };

  const handleAddBidContainerClose = () => {
    setIsAddBidContainerVisible(false);
    udpatePDFViewerContainer();
    if (selectedTextInfo) setSelectedTextInfo(null);
  };

  const onToolBarClick = (args: any) => {
    switch (args.item.id) {
      case CustomToolbarItems.add:
        onAddClick();
        break;
      case CustomToolbarItems.previousPage:
        onPreviousPage();
        break;
      case CustomToolbarItems.nextPage:
        onNextPage();
        break;
      case CustomToolbarItems.saveAnnotation:
        saveAnnotations();
        break;

      default:
        break;
    }
  };

  const isPreviousPageDisabled = useMemo(() => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length === 1 || index === 0) return true;
      return false;
    }
    const length = specificationsThumbnails.length;
    const index = specificationsThumbnails.findIndex(
      thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
    );
    if (length === 1 || index === 0) return true;
    return false;
  }, [
    drawingsThumbnails,
    selectedThumbnailInfo?.document?.inputDocumentType,
    selectedThumbnailInfo?.sheet?.sheetId,
    specificationsThumbnails
  ]);

  const isNextPageDisabled = useMemo(() => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length === 1 || index === length - 1) return true;
      return false;
    }
    const length = specificationsThumbnails.length;
    const index = specificationsThumbnails.findIndex(
      thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
    );
    if (length === 1 || index === length - 1) return true;
    return false;
  }, [
    drawingsThumbnails,
    selectedThumbnailInfo?.document?.inputDocumentType,
    selectedThumbnailInfo?.sheet?.sheetId,
    specificationsThumbnails
  ]);

  const onTextSelectionEnd = (args: any) => {
    if (args) setSelectedTextInfo(args as TextSelectionProps);
    else setSelectedTextInfo(null);
  };

  return (
    <Container isLoading={isLoadingDocument}>
      {pdfUrl && isPdfReady && (
        <PdfViewerComponent
          key={pdfUrl}
          id='container'
          ref={viewer}
          documentPath={pdfUrl}
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          width='100%'
          style={{ flex: 3 }}
          height={isThumbnailsDrawerMinimised ? '98%' : '88%'}
          maxZoom={800}
          isExtractText
          annotationAdd={onAddAnnotation}
          extractTextOption={ExtractTextOption.TextAndBounds}
          documentLoad={onDocumentLoad}
          pageRenderComplete={onPageRendered}
          extractTextCompleted={onExtractText}
          toolbarClick={onToolBarClick}
          textSelectionEnd={onTextSelectionEnd}
          downloadFileName={`${selectedThumbnailInfo?.sheet.sheetNumber} - ${
            selectedThumbnailInfo?.sheet?.title
          }`}
          pageOrganizerSettings={{
            canCopy: false,
            canDelete: false,
            canImport: false,
            canInsert: false,
            canRearrange: false,
            canRotate: true
          }}
          toolbarSettings={{
            showTooltip: true,
            toolbarItems: [
              {
                tooltipText: 'Add bid/ Scope',
                id: CustomToolbarItems.add,
                align: 'right',
                text: '+',
                type: 'Button',
                cssClass: 'e-pv-add-button'
              },
              {
                tooltipText: 'Save Changes',
                id: CustomToolbarItems.saveAnnotation,
                align: 'right',
                text: 'Save',
                type: 'Button',
                prefixIcon: 'e-save'
              },
              {
                tooltipText: 'Previous page',
                id: CustomToolbarItems.previousPage,
                align: 'right',
                text: 'Next',
                type: 'Button',
                prefixIcon: 'e-pv-previous-page-navigation-icon',
                cssClass: isPreviousPageDisabled
                  ? 'e-pv-previous-page-navigation-icon-disabled'
                  : ''
              },
              {
                tooltipText: 'Next page',
                id: CustomToolbarItems.nextPage,
                align: 'right',
                text: 'Prev',
                type: 'Button',
                prefixIcon: 'e-pv-next-page-navigation-icon',
                cssClass: isNextPageDisabled ? 'e-pv-next-page-navigation-icon-diabled' : ''
              },
              'UndoRedoTool',
              'PanTool',
              'SelectionTool',
              'CommentTool',
              'AnnotationEditTool',
              'SearchOption',
              'MagnificationTool',
              'DownloadOption'
            ]
          }}
        >
          <Inject
            services={[
              Toolbar,
              Magnification,
              Navigation,
              Annotation,
              LinkAnnotation,
              Print,
              TextSelection,
              TextSearch,
              FormFields,
              FormDesigner,
              PageOrganizer
            ]}
          />
        </PdfViewerComponent>
      )}
      {isAddBidContainerVisible && (
        <AddBidItem
          isThumbnailDrawerMinimized={isThumbnailsDrawerMinimised}
          onBidItemAdded={onBidItemAdded}
          onClose={handleAddBidContainerClose}
          bidItemName={selectedTextInfo?.textContent}
        />
      )}
      <AddBidInstructionsModal
        visible={isAddInstructionsModalVisible}
        onClose={() => setIsAddInstructionsModalVisible(false)}
        onContinue={() => setIsAddInstructionsModalVisible(false)}
      />
    </Container>
  );
};

export default React.memo(PDFViewer);
export type { TextSelectionProps };
