import {
  PdfViewerComponent,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  Print,
  TextSelection,
  Annotation,
  TextSearch,
  FormFields,
  FormDesigner,
  Inject,
  ExtractTextOption,
  AnnotBounds,
  PageOrganizer,
  AnnotationDataFormat,
  PageInfoModel
} from '@syncfusion/ej2-react-pdfviewer';

import './index.css';
import { useMutation, useQuery } from '@tanstack/react-query';
import { App } from 'antd';
import { PDFDocument } from 'pdf-lib';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { DocumentWithPresignedUrlDTOInputDocumentTypeEnum, NoteDTO } from 'src/api';
import { noteAPI, sheetAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import AddBidItem from './addBidItem';
import AddBidInstructionsModal from './components/AddBidInstructionsModal';
import useScopesStore from './store/useScopesStore';
import { StorageKey, queryKeys } from '../utils/constant';
import { getItem } from '../utils/storage';
import BidItemsList from './components/BidItemsList';
import { getSpecDocS3Url } from '../utils/thumbnailInfo';

const Container = styled.div<{ isLoading: boolean; isThumbnailsDrawerMinimised: boolean }>`
  height: ${({ isLoading, isThumbnailsDrawerMinimised }) => {
    if (isLoading) {
      return '0%';
    }
    if (isThumbnailsDrawerMinimised) {
      return '98%';
    }
    return '88%';
  }};
  overflow: auto;
  width: 100%;
  display: flex;
  gap: 20px;
`;

const AddBidContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  gap: 4px;
`;

enum CustomToolbarItems {
  add = 'add',
  previousPage = 'previous_page',
  nextPage = 'next_page',
  saveAnnotation = 'save_annotation'
}

type TextBounds = {
  left: number;
  right: number;
  top: number;
  bottom: number;
  width: number;
  height: number;
  pageIndex: number;
};

type TextSelectionProps = {
  name: string;
  textContent: string;
  textBounds: Array<TextBounds>;
  pageIndex: number;
};

interface PageSpec {
  width?: number;
  height?: number;
}

const pointToPixel = 96 / 72;
const PDFViewer: React.FC<{
  isThumbnailsDrawerMinimised: boolean;
}> = ({ isThumbnailsDrawerMinimised }) => {
  const viewer = useRef<PdfViewerComponent>(null);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const {
    isScopeSubMenuOpen,
    isSideMenuOpen,
    setIsSideMenuOpen,
    setScopeSubMenuOpen,
    selectedProjectId,
    currentUser
  } = useGlobalStore();
  const [extractedText, setExtractedText] = useState<Array<{ Bounds: AnnotBounds; Text: string }>>(
    []
  );
  const [activeAnnotation, setActiveAnnotation] = useState<any>();
  const [originalPageSize, setOriginalPageSize] = useState<any>();
  const [isAddInstructionsModalVisible, setIsAddInstructionsModalVisible] =
    useState<boolean>(false);
  const [selectedTextInfo, setSelectedTextInfo] = useState<TextSelectionProps | null>(null);
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const [isPageRendered, setIsPageRendered] = useState<boolean>(false);
  const [isAddBidContainerVisible, setIsAddBidContainerVisible] = useState<boolean>(false);
  const [importedAnnotations, setImportedAnnotations] = useState<boolean>(false);
  const [scopeKeywordsHighlighted, setScopeKeywordsHighlighted] = useState<boolean>(false);
  const {
    selectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails,
    setSelectedThumbnailInfo,
    setIsLoadingDocument,
    isLoadingDocument
  } = useScopesStore();
  const { scopeId } = useParams();
  const { notification } = App.useApp();

  const { data: sheetInfo } = useQuery({
    queryKey: [queryKeys.scopeInfo, selectedThumbnailInfo?.sheet?.sheetId, scopeId],
    queryFn: () =>
      sheetAPI.getSheetById(selectedThumbnailInfo?.sheet?.sheetId || 0, Number(scopeId)),
    select: res => res.data,
    enabled: !!selectedThumbnailInfo?.sheet?.sheetId && !!scopeId
  });

  const onNotesSaved = () => {
    refetch();
    notification.success({ message: 'Saved document successfully' });
  };

  const {
    mutateAsync: createNotes,
    status: createNotesStatus,
    reset: resetCreateNotes
  } = useMutation({
    mutationFn: (data: NoteDTO) => noteAPI.createNote(data),
    onSuccess: () => {
      onNotesSaved();
    }
  });

  const {
    mutateAsync: updateNotes,
    status: updateNotesStatus,
    reset: resetUpdateNotes
  } = useMutation({
    mutationFn: (props: { id: number; data: NoteDTO }) => noteAPI.updateNote(props.id, props.data),
    onSuccess: () => {
      onNotesSaved();
    }
  });

  const {
    mutateAsync: deleteNotes,
    status: deleteNotesStatus,
    reset: resetDeleteNotes
  } = useMutation({
    mutationFn: (id: number) => noteAPI.deleteNote(id),
    onSuccess: () => {
      onNotesSaved();
    }
  });

  const { data: notesData, refetch } = useQuery({
    queryKey: [
      queryKeys.getNotes,
      scopeId,
      selectedProjectId,
      selectedThumbnailInfo?.document?.id,
      selectedThumbnailInfo?.sheet?.sheetId,
      selectedThumbnailInfo?.specification?.specificationId
    ],
    queryFn: () =>
      noteAPI.getNotes(
        undefined,
        Number(selectedProjectId),
        selectedThumbnailInfo?.document?.id,
        Number(scopeId),
        undefined,
        selectedThumbnailInfo?.sheet?.sheetId,
        selectedThumbnailInfo?.specification?.specificationId
      ),
    select: res => res.data.content,
    enabled: !!(
      selectedProjectId &&
      selectedThumbnailInfo?.document?.id &&
      scopeId &&
      (selectedThumbnailInfo?.sheet?.sheetId ||
        selectedThumbnailInfo?.specification?.specificationId)
    )
  });

  useEffect(() => {
    if (
      notesData &&
      notesData.length > 0 &&
      viewer.current &&
      isPageRendered &&
      !importedAnnotations
    ) {
      const note = notesData[0];
      if (note && note.annotationData) {
        try {
          const annotationData = note.annotationData;
          viewer?.current?.importAnnotation(JSON.parse(annotationData), AnnotationDataFormat.Json);
          setImportedAnnotations(true);
        } catch (error) {
          console.error('Error importing annotations:', error);
        }
      }
    }
  }, [notesData, isPageRendered, importedAnnotations]);

  function rotateCoords(
    coords?: number[],
    width?: number,
    height?: number,
    tWidth?: number,
    tHeight?: number,
    angle?: number
  ): { str: string; arr: number[] } {
    if (coords && width && height && tWidth && tHeight) {
      const rotated: number[] = [];

      for (let i = 0; i < coords.length; i += 2) {
        const x = coords[i];
        const y = coords[i + 1];

        let newX = x;
        let newY = y;

        switch (angle) {
          case 0:
            // No rotation
            newX = x;
            newY = height - y;
            break;
          case 90:
            // 90° clockwise
            newX = y;
            newY = x;
            break;
          case 180:
            // 180°
            newX = width - x;
            newY = y;
            break;
          case 270:
            // 270° clockwise (or 90° counter-clockwise)
            newX = height - y;
            newY = width - x;
            break;
          default:
            throw new Error('Unsupported rotation angle. Use 0, 90, 180, or 270.');
        }

        rotated.push(newX, newY);
      }
      let scale = 1;

      if (tWidth > tHeight) {
        scale = tWidth / width;
      } else {
        scale = tHeight / height;
      }
      const scaled = rotated?.map(coord => coord * scale);

      const scaledX = [scaled[0], scaled[2], scaled[4], scaled[6]];
      const scaledY = [scaled[1], scaled[3], scaled[5], scaled[7]];

      const scaledArr = [
        Math.min(...scaledX),
        Math.min(...scaledY),
        Math.max(...scaledX),
        Math.min(...scaledY),
        Math.min(...scaledX),
        Math.max(...scaledY),
        Math.max(...scaledX),
        Math.max(...scaledY)
      ];
      const scaledStr = scaledArr.join(',');
      return { str: scaledStr, arr: scaledArr };
    }
    return { str: '', arr: [] };
  }

  const calculateZoomCoords = (
    coords?: number[],
    width?: number,
    height?: number,
    tWidth?: number,
    tHeight?: number
  ) => {
    if (coords && width && height && tWidth && tHeight) {
      let scale = 1;

      if (tWidth > tHeight) {
        scale = tWidth / width;
      } else {
        scale = tHeight / height;
      }
      const adjustFactor = tWidth > tHeight ? tWidth * 0.1 : tHeight * 0.1;
      const adjustScaleX = tWidth / tHeight;
      const adjustScaleY = tHeight / tWidth;
      const scaledX = coords[0] * scale * pointToPixel;
      const scaledY = coords[1] * scale * pointToPixel;

      return {
        x:
          scaledX > adjustFactor * adjustScaleX
            ? scaledX - adjustFactor * adjustScaleX
            : scaledX * 0.1,
        y:
          scaledY > adjustFactor * adjustScaleY
            ? scaledY - adjustFactor * adjustScaleY
            : scaledY * 0.1,
        width: 100 * adjustScaleY,
        height: 100 * adjustScaleX
      };
    }
    return null;
  };

  const handleZoomToRect = (
    x: number,
    y: number,
    width: number,
    height: number,
    pageNumber: number
  ) => {
    if (viewer && viewer.current) {
      const pagePoint = { x, y } as any;
      const clientPoint = viewer.current.convertPagePointToClientPoint(
        pagePoint,
        Number(pageNumber)
      );
      const rect = new DOMRect(clientPoint.x, clientPoint.y, width, height) as any;
      viewer.current.zoomToRect(rect);
    }
  };

  useEffect(() => {
    if (isPageRendered && sheetInfo && !scopeKeywordsHighlighted) {
      try {
        const source: PageSpec = { width: sheetInfo.pageWidth, height: sheetInfo.pageHeight };
        const target: PageInfoModel | undefined = viewer.current?.getPageInfo(
          viewer.current?.currentPageNumber - 1
        );
        let tempAnnotations: any = [];
        let zoomCoords: any = null;
        sheetInfo.regionDetail?.map((quad, index) => {
          const newQuad = rotateCoords(
            quad.quadPx,
            source.width,
            source.height,
            target?.width,
            target?.height,
            target?.rotation
          );
          if (index === 0) {
            zoomCoords = calculateZoomCoords(
              quad.quadPx,
              source.width,
              source.height,
              target?.width,
              target?.height
            );
          }
          if (newQuad.str) {
            const itemHighlight = {
              type: 'Highlight',
              page: '0',
              rect: {
                x: '',
                y: '',
                width: '',
                height: ''
              },
              title: 'Guest',
              subject: 'Highlight',
              date: "D:20250515151547+05'30'",
              name: `c55b856e-566c-4f6b-184e-deea5464be1b-${index}`,
              color: themeTokens.lightGreen,
              opacity: '1',
              flags: 'print',
              width: '1',
              coords: `${newQuad.str}`
            };
            tempAnnotations = [...tempAnnotations, itemHighlight];
          }
        });
        const tempAnnotationsJson = {
          pdfAnnotation: {
            '0': {
              shapeAnnotation: [...tempAnnotations]
            }
          }
        };
        viewer?.current?.importAnnotation(tempAnnotationsJson, AnnotationDataFormat.Json);
        setScopeKeywordsHighlighted(true);
        if (zoomCoords !== null) {
          handleZoomToRect(
            zoomCoords.x,
            zoomCoords.y,
            zoomCoords.width,
            zoomCoords.height,
            viewer?.current?.currentPageNumber ?? 1
          );
        }
      } catch (error) {
        console.error('Error importing annotations:', error);
      }
    }
  }, [sheetInfo, isPageRendered, scopeKeywordsHighlighted]);

  useEffect(() => {
    if (pdfUrl) {
      setTimeout(() => {
        setIsPdfReady(true);
      }, 100);
    } else {
      setIsPdfReady(false);
    }
  }, [pdfUrl]);

  useEffect(() => {
    setIsPageRendered(false);
    setScopeKeywordsHighlighted(false);
    setImportedAnnotations(false);
  }, [scopeId]);

  async function extractAndCreateChildPDF() {
    if (selectedThumbnailInfo) {
      if (
        selectedThumbnailInfo?.document?.inputDocumentType ===
        DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing
      ) {
        if (
          !selectedThumbnailInfo?.document?.presignedUrl ||
          !selectedThumbnailInfo?.sheet?.pageNumber
        )
          return;
        try {
          setIsLoadingDocument(true);
          const response = await fetch(selectedThumbnailInfo?.document?.presignedUrl);
          if (!response.ok) {
            throw new Error('Failed to fetch the PDF');
          }
          const existingPdfBytes = await response.arrayBuffer();
          const parentPdfDoc = await PDFDocument.load(existingPdfBytes);
          const childPdfDoc = await PDFDocument.create();
          const [extractedPage] = await childPdfDoc.copyPages(parentPdfDoc, [
            selectedThumbnailInfo.sheet.pageNumber - 1
          ]);
          childPdfDoc.addPage(extractedPage);
          const childPdfBytes = await childPdfDoc.save({
            useObjectStreams: false,
            addDefaultPage: false
          });
          const blob = new Blob([childPdfBytes], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);
          setPdfUrl(url);
        } catch (error) {
          console.error('Error generating the child PDF:', error);
        }
      } else {
        setIsLoadingDocument(true);
        const code = selectedThumbnailInfo?.specification?.specificationCode;
        const modifiedCode = code?.replace(/ /g, '+');
        const url = getSpecDocS3Url(
          currentUser?.companyId,
          Number(selectedProjectId),
          selectedThumbnailInfo?.document?.id,
          modifiedCode
        );
        setPdfUrl(url);
      }
    }
  }

  useEffect(() => {
    extractAndCreateChildPDF();
    if (isPageRendered) setIsPageRendered(false);
    if (selectedTextInfo) setSelectedTextInfo(null);
    if (importedAnnotations) setImportedAnnotations(false);
    if (scopeKeywordsHighlighted) setScopeKeywordsHighlighted(false);
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [selectedThumbnailInfo]);

  useEffect(() => {
    if (viewer.current) {
      //Adding small delay to ensure viewer container updates only after animation of side bar is completed
      udpatePDFViewerContainer(500);
    }
  }, [isSideMenuOpen, isScopeSubMenuOpen, isThumbnailsDrawerMinimised]);

  const onAddAnnotation = async (e: any) => {
    setActiveAnnotation(e);
  };

  const extractAnnotationImage = async (e: any) => {
    const viewerRef = viewer?.current;
    if (!viewerRef || !selectedThumbnailInfo?.thumbnail) return;
    //Note: For not breaking pixels on image follow these steps: exportAsImage -> dimensions from pageInfo -> draw image on canvas with those dimensions -> use that for cropping
    const image = new Image();
    image.src = selectedThumbnailInfo.thumbnail;
    const coordinates = e.annotationBound;
    const { top, left, height, width } = coordinates;
    const pixelToPoints = 72 / 96;
    const sx = left * pixelToPoints;
    const sy = top * pixelToPoints;
    const sw = width * pixelToPoints;
    const sh = height * pixelToPoints;

    const croppedCanvas = document.createElement('canvas');
    croppedCanvas.width = sw;
    croppedCanvas.height = sh;

    const ctx = croppedCanvas.getContext('2d');
    if (!ctx) return;

    ctx.drawImage(image, sx, sy, sw, sh, 0, 0, sw, sh);

    const imageUrl = croppedCanvas.toDataURL('image/png');
  };

  const onExtractText = (e: any) => {
    const text = e.documentTextCollection[0][0].TextData;
    const originalPageSize = e.documentTextCollection[0][0].PageSize;
    setExtractedText(text);
    setOriginalPageSize(originalPageSize);
  };

  const onPageRendered = () => {
    setIsPageRendered(true);
  };

  const onDocumentLoad = () => {
    setIsLoadingDocument(false);
  };

  const extractTextOverArea = async () => {
    if (activeAnnotation && viewer.current) {
      const pageInfo = viewer.current.getPageInfo(0);
      if (pageInfo.height) {
        const scaleFactor = pageInfo?.height / originalPageSize.Height;
        const scaledBounds = {
          height: activeAnnotation.annotationBound.height * scaleFactor,
          left: activeAnnotation.annotationBound.left * scaleFactor,
          top: activeAnnotation.annotationBound.top * scaleFactor,
          width: activeAnnotation.annotationBound.width * scaleFactor
        };
        const filteredData = extractedText.filter(item => {
          if (
            item.Bounds.Left >= scaledBounds.left &&
            item.Bounds.Top >= scaledBounds.top &&
            item.Bounds.Right <= scaledBounds.left + scaledBounds.width &&
            item.Bounds.Bottom <= scaledBounds.top + scaledBounds.height
          )
            return item;
        });
        if (filteredData?.length > 0) {
          const extractedText = filteredData?.map(itm => itm.Text).join('');
        }
      }
    }
  };

  const onBidItemAdded = async () => {
    if (viewer.current && selectedTextInfo) {
      const bounds = selectedTextInfo.textBounds?.map(itm => ({
        width: itm.width,
        y: itm.top,
        x: itm.left,
        height: itm.height
      }));
      viewer.current.annotation.addAnnotation('Highlight', {
        bounds,
        pageNumber: selectedTextInfo?.pageIndex
      } as any);
      saveAnnotations();
    }
    setSelectedTextInfo(null);
  };

  const onPreviousPage = () => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    const selectedSpecId = selectedThumbnailInfo?.specification?.specificationId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== 0) {
        setSelectedThumbnailInfo(drawingsThumbnails[index - 1]);
      }
    } else {
      const length = specificationsThumbnails.length;
      const index = specificationsThumbnails.findIndex(
        thumbnail => thumbnail?.specification?.specificationId === selectedSpecId
      );
      if (length > 1 && index !== 0) {
        setSelectedThumbnailInfo(specificationsThumbnails[index - 1]);
      }
    }
  };

  const onNextPage = () => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    const selectedSpecId = selectedThumbnailInfo?.specification?.specificationId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length > 1 && index !== length - 1) {
        setSelectedThumbnailInfo(drawingsThumbnails[index + 1]);
      }
    } else {
      const length = specificationsThumbnails.length;
      const index = specificationsThumbnails.findIndex(
        thumbnail => thumbnail?.specification?.specificationId === selectedSpecId
      );
      if (length > 1 && index !== length - 1) {
        setSelectedThumbnailInfo(specificationsThumbnails[index + 1]);
      }
    }
  };

  const saveAnnotations = async () => {
    const annotations = await viewer.current?.exportAnnotationsAsObject();
    if (annotations) {
      const sheetId = selectedThumbnailInfo?.sheet?.sheetId;
      const specificationId = selectedThumbnailInfo?.specification?.specificationId;
      if (
        selectedProjectId &&
        selectedThumbnailInfo?.document?.id &&
        (sheetId || specificationId)
      ) {
        let data: NoteDTO = {
          scopeId: scopeId ? Number(scopeId) : undefined,
          projectId: Number(selectedProjectId),
          documentId: selectedThumbnailInfo.document.id,
          annotationData: JSON.stringify(annotations)
        };
        if (sheetId) {
          data = { ...data, sheetId };
        } else if (specificationId) {
          data = {
            ...data,
            specificationId
          };
        }
        try {
          if (notesData && notesData.length > 0) {
            const note = notesData[0];
            if (note && note.id) {
              await updateNotes({ id: note.id, data });
            } else {
              await createNotes(data);
            }
          } else {
            await createNotes(data);
          }
        } catch (error) {
          console.error(error);
        } finally {
          resetCreateNotes();
          resetUpdateNotes();
        }
      }
    } else {
      try {
        if (notesData && notesData.length > 0) {
          notesData?.map((note: NoteDTO) => {
            if (note.id) deleteNotes(note.id);
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        resetDeleteNotes();
      }
    }
  };

  const udpatePDFViewerContainer = (delay?: number) => {
    setTimeout(() => {
      viewer?.current?.updateViewerContainer();
    }, delay || 100);
  };

  const onAddClick = () => {
    if (selectedTextInfo) {
      setIsAddBidContainerVisible(true);
      if (isSideMenuOpen && !isScopeSubMenuOpen) {
        udpatePDFViewerContainer();
      } else {
        setIsSideMenuOpen(true);
        setScopeSubMenuOpen(false);
      }
    } else {
      const isDontShowAgain = getItem(StorageKey.instructionsModalChecked);
      if (!isDontShowAgain) {
        setIsAddInstructionsModalVisible(true);
      } else {
        notification.error({ message: 'Please select text to continue' });
      }
    }
  };

  const handleAddBidContainerClose = () => {
    setIsAddBidContainerVisible(false);
    udpatePDFViewerContainer();
    if (selectedTextInfo) setSelectedTextInfo(null);
  };

  const onToolBarClick = (args: any) => {
    switch (args.item.id) {
      case CustomToolbarItems.add:
        onAddClick();
        break;
      case CustomToolbarItems.previousPage:
        onPreviousPage();
        break;
      case CustomToolbarItems.nextPage:
        onNextPage();
        break;
      case CustomToolbarItems.saveAnnotation:
        saveAnnotations();
        break;

      default:
        break;
    }
  };

  const isPreviousPageDisabled = useMemo(() => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    const specificationId = selectedThumbnailInfo?.specification?.specificationId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length === 1 || index === 0) return true;
      return false;
    }
    const length = specificationsThumbnails.length;
    const index = specificationsThumbnails.findIndex(
      thumbnail => thumbnail?.specification?.specificationId === specificationId
    );
    if (length === 1 || index === 0) return true;
    return false;
  }, [
    drawingsThumbnails,
    selectedThumbnailInfo?.document?.inputDocumentType,
    selectedThumbnailInfo?.sheet?.sheetId,
    selectedThumbnailInfo?.specification?.specificationId,
    specificationsThumbnails
  ]);

  const isNextPageDisabled = useMemo(() => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    const selectedSheetId = selectedThumbnailInfo?.sheet?.sheetId;
    const specificationId = selectedThumbnailInfo?.specification?.specificationId;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      const length = drawingsThumbnails.length;
      const index = drawingsThumbnails.findIndex(
        thumbnail => thumbnail?.sheet?.sheetId === selectedSheetId
      );
      if (length === 1 || index === length - 1) return true;
      return false;
    }
    const length = specificationsThumbnails.length;
    const index = specificationsThumbnails.findIndex(
      thumbnail => thumbnail?.specification?.specificationId === specificationId
    );
    if (length === 1 || index === length - 1) return true;
    return false;
  }, [
    drawingsThumbnails,
    selectedThumbnailInfo?.document?.inputDocumentType,
    selectedThumbnailInfo?.sheet?.sheetId,
    selectedThumbnailInfo?.specification?.specificationId,
    specificationsThumbnails
  ]);

  const onTextSelectionEnd = (args: any) => {
    if (args) setSelectedTextInfo(args as TextSelectionProps);
    else setSelectedTextInfo(null);
  };

  const downloadFileName = useMemo(() => {
    const selectedType = selectedThumbnailInfo?.document?.inputDocumentType;
    if (selectedType === DocumentWithPresignedUrlDTOInputDocumentTypeEnum.Drawing) {
      return `${selectedThumbnailInfo?.sheet?.sheetNumber} - ${selectedThumbnailInfo?.sheet?.title}`;
    }
    return `${selectedThumbnailInfo?.specification?.specificationCode} - ${selectedThumbnailInfo?.specification?.specificationTitle}`;
  }, [
    selectedThumbnailInfo?.document?.inputDocumentType,
    selectedThumbnailInfo?.sheet?.sheetNumber,
    selectedThumbnailInfo?.sheet?.title,
    selectedThumbnailInfo?.specification?.specificationCode,
    selectedThumbnailInfo?.specification?.specificationTitle
  ]);

  return (
    <Container
      isLoading={isLoadingDocument}
      isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised}
    >
      {pdfUrl && isPdfReady && (
        <PdfViewerComponent
          key={`${pdfUrl}-${scopeId}`}
          id='container'
          ref={viewer}
          documentPath={pdfUrl}
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          width='100%'
          height='100%'
          style={{ flex: 3 }}
          maxZoom={800}
          isExtractText
          annotationAdd={onAddAnnotation}
          extractTextOption={ExtractTextOption.TextAndBounds}
          documentLoad={onDocumentLoad}
          pageRenderComplete={onPageRendered}
          extractTextCompleted={onExtractText}
          toolbarClick={onToolBarClick}
          textSelectionEnd={onTextSelectionEnd}
          downloadFileName={downloadFileName}
          pageOrganizerSettings={{
            canCopy: false,
            canDelete: false,
            canImport: false,
            canInsert: false,
            canRearrange: false,
            canRotate: true
          }}
          toolbarSettings={{
            showTooltip: true,
            toolbarItems: [
              {
                tooltipText: 'Add Bid Item',
                id: CustomToolbarItems.add,
                align: 'right',
                text: '+',
                type: 'Button',
                cssClass: 'e-pv-add-button'
              },
              {
                tooltipText: 'Save Changes',
                id: CustomToolbarItems.saveAnnotation,
                align: 'right',
                text: 'Save',
                type: 'Button',
                prefixIcon: 'e-save'
              },
              {
                tooltipText: 'Previous page',
                id: CustomToolbarItems.previousPage,
                align: 'right',
                text: 'Next',
                type: 'Button',
                prefixIcon: 'e-pv-previous-page-navigation-icon',
                cssClass: isPreviousPageDisabled
                  ? 'e-pv-previous-page-navigation-icon-disabled'
                  : ''
              },
              {
                tooltipText: 'Next page',
                id: CustomToolbarItems.nextPage,
                align: 'right',
                text: 'Prev',
                type: 'Button',
                prefixIcon: 'e-pv-next-page-navigation-icon',
                cssClass: isNextPageDisabled ? 'e-pv-next-page-navigation-icon-diabled' : ''
              },
              'UndoRedoTool',
              'PanTool',
              'SelectionTool',
              'CommentTool',
              'AnnotationEditTool',
              'SearchOption',
              'MagnificationTool',
              'DownloadOption'
            ]
          }}
        >
          <Inject
            services={[
              Toolbar,
              Magnification,
              Navigation,
              Annotation,
              LinkAnnotation,
              Print,
              TextSelection,
              TextSearch,
              FormFields,
              FormDesigner,
              PageOrganizer
            ]}
          />
        </PdfViewerComponent>
      )}
      {isAddBidContainerVisible && (
        <AddBidContainer>
          <AddBidItem
            onBidItemAdded={onBidItemAdded}
            onClose={handleAddBidContainerClose}
            bidItemName={selectedTextInfo?.textContent}
          />
          <BidItemsList />
        </AddBidContainer>
      )}
      <AddBidInstructionsModal
        visible={isAddInstructionsModalVisible}
        onClose={() => setIsAddInstructionsModalVisible(false)}
        onContinue={() => setIsAddInstructionsModalVisible(false)}
      />
    </Container>
  );
};

export default React.memo(PDFViewer);
export type { TextSelectionProps };
