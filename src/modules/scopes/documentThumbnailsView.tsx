import { LoadingOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { Button, Flex, Spin } from 'antd';
import { useState } from 'react';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import FileViewer from './fileViewer';
import PDFViewer from './PDFViewer';
import useScopesStore from './store/useScopesStore';

export enum DocumentTabName {
  drawings = 'Drawings',
  specs = 'Specs'
}

const Container = styled.div`
  position: relative;
  height: 100%;
`;

const ContentContainer = styled.div`
  height: 100%;
`;

const SearchPanelWrapper = styled.div<{
  isThumbnailsOpen: boolean;
  isThumbnailsDrawerMinimised: boolean;
}>`
  position: absolute;
  bottom: 0;
  left: 0;
  height: ${props => {
    if (props.isThumbnailsDrawerMinimised) return '40px';
    else if (props.isThumbnailsOpen) return 'calc(100vh - 60px)';
    return '180px';
  }};
  width: 100%;
  transition: height 0.5s ease-in-out;
  z-index: 200;
`;

const StyledButton = styled.div<{ active?: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  width: 100px;
  border: ${({ active }) => (active ? 'none' : '1px solid #E6E6E6')};
  background: ${({ active }) =>
    active ? themeTokens.menuSubMenuItemSelectedBg : themeTokens.textLight};
  color: ${({ active }) => (active ? themeTokens.textLight : themeTokens.buttonDark)};
`;

const PDFViewerContainer = styled.div<{
  isThumbnailsDrawerMinimised: boolean;
}>`
  height: ${props => (props.isThumbnailsDrawerMinimised ? '98%' : '88%')};
  width: 100%;
`;

const EmptyContentContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  margin-top: 20vh;
`;

const LoaderContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  .loader {
    position: absolute;
    top: 30%;
  }
`;

const Image = styled.img`
  width: auto;
  height: 85%;
  object-fit: contain;
  align-self: flex-start;
`;

const DocumentThumbnailsView: React.FC<{
  isThumbnailsOpen: boolean;
  setIsThumbnailsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isThumbnailsDrawerMinimised: boolean;
  setIsThumbnailsDrawerMinised: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({
  isThumbnailsOpen,
  setIsThumbnailsOpen,
  isThumbnailsDrawerMinimised,
  setIsThumbnailsDrawerMinised
}) => {
  const [activeTab, setActiveTab] = useState<DocumentTabName>(DocumentTabName.drawings);
  const {
    selectedThumbnailInfo,
    drawingsThumbnails,
    specificationsThumbnails,
    isCreatingThumbnails,
    isLoadingDocument
  } = useScopesStore();

  if (isCreatingThumbnails) return <Spin indicator={<LoadingOutlined spin />} size='large' />;

  return (
    <Container>
      {drawingsThumbnails.length > 0 || specificationsThumbnails.length > 0 ? (
        <ContentContainer>
          <PDFViewerContainer isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised}>
            {isLoadingDocument && (
              <LoaderContainer>
                <Image
                  src={selectedThumbnailInfo?.thumbnail}
                  alt={selectedThumbnailInfo?.sheet.title}
                />
                <Spin className='loader' indicator={<LoadingOutlined spin />} size='large' />
              </LoaderContainer>
            )}

            {selectedThumbnailInfo ? (
              <PDFViewer isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised} />
            ) : (
              <div>Please select a thumbnail to preview</div>
            )}
          </PDFViewerContainer>
          <SearchPanelWrapper
            isThumbnailsOpen={isThumbnailsOpen}
            isThumbnailsDrawerMinimised={isThumbnailsDrawerMinimised}
          >
            <Flex
              justify={
                !isThumbnailsDrawerMinimised && !isThumbnailsOpen ? 'space-between' : 'flex-end'
              }
            >
              <Flex gap={4}>
                {!isThumbnailsDrawerMinimised &&
                  !isThumbnailsOpen &&
                  Object.values(DocumentTabName).map(tab => (
                    <StyledButton
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      active={activeTab === tab}
                    >
                      {tab}
                    </StyledButton>
                  ))}
              </Flex>
              <Flex gap={4}>
                {!isThumbnailsDrawerMinimised && (
                  <Button
                    onClick={() => setIsThumbnailsOpen(isThumbnailsOpen => !isThumbnailsOpen)}
                  >
                    {isThumbnailsOpen ? 'Close' : 'Search'}
                  </Button>
                )}
                <Button
                  onClick={() => {
                    if (isThumbnailsDrawerMinimised) {
                      setIsThumbnailsOpen(false);
                      setIsThumbnailsDrawerMinised(false);
                    } else {
                      setIsThumbnailsDrawerMinised(true);
                    }
                  }}
                  icon={isThumbnailsDrawerMinimised ? <UpOutlined /> : <DownOutlined />}
                />
              </Flex>
            </Flex>
            <FileViewer
              isThumbnailsOpen={isThumbnailsOpen}
              drawingsThumbnails={drawingsThumbnails}
              specificationsThumbnails={specificationsThumbnails}
            />
          </SearchPanelWrapper>
        </ContentContainer>
      ) : (
        <EmptyContentContainer>No documents found for the Bid item</EmptyContentContainer>
      )}
    </Container>
  );
};

export default DocumentThumbnailsView;
