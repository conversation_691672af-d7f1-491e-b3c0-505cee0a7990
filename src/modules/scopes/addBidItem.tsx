import { CaretLeftFilled } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Form, Input, App, Button } from 'antd';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { BidItemDTO, MasterScopeDTO, ScopeDTO, ScopeUpdateDTO } from 'src/api';
import { bidItemAPI, masterScopeAPI, scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import useScopesStore from './store/useScopesStore';
import CreatableSearchSelect from '../common/creatableSearchSelect';
import { queryKeys, disciplines, APIMutationStatus } from '../utils/constant';

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
`;

const Title = styled.div`
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 32px;
`;

const Container = styled.div<{ isThumbnailDrawerMinimized: boolean }>`
  flex: 1;
  width: 100%;
  height: ${props => (props.isThumbnailDrawerMinimized ? '98%' : '88%')};
  background-color: ${themeTokens.whiteBg};
  padding: 20px;
  position: relative;
`;

const LeftPointer = styled(CaretLeftFilled)`
  position: absolute;
  left: -24px;
  top: 50%;
  font-size: 34px;
  color: white;
`;

interface AddBidModalProps {
  onClose: () => void;
  bidItemName?: string;
  onBidItemAdded: () => void;
  isThumbnailDrawerMinimized: boolean;
}

const AddBidItem: React.FC<AddBidModalProps> = ({
  onClose,
  bidItemName,
  onBidItemAdded,
  isThumbnailDrawerMinimized
}) => {
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();
  const { selectedThumbnailInfo } = useScopesStore();
  const { selectedProjectId } = useGlobalStore();
  const { scopeId } = useParams();
  const { notification } = App.useApp();

  const { data: scopesList } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        500,
        ['division,asc']
      ),
    retry: false,
    enabled: !!selectedProjectId,
    select: res => res.data?.content
  });

  const { data: masterScopesData } = useQuery({
    queryKey: [queryKeys.masterScopes],
    queryFn: () =>
      masterScopeAPI.getMasterScopes(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        0, // page
        1000 // size
      ),

    select: response => response.data?.content,
    staleTime: 5 * 60 * 1000
  });

  const onScopeUpdateSuccess = useCallback(
    (message: string) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allScopes] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.scopeInfo] });
      notification.success({ message });
    },
    [queryClient, notification]
  );

  const {
    mutateAsync: createBidItem,
    reset: resetCreate,
    isPending
  } = useMutation({
    mutationFn: (data: BidItemDTO) => bidItemAPI.createBidItem(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] });
      notification.success({ message: 'Bid Item created successfully' });
    }
  });

  const {
    mutateAsync: updateScopeMutate,
    status: updateStatus,
    reset: updateReset
  } = useMutation({
    mutationFn: (data: { id: number; updateData: ScopeUpdateDTO }) =>
      scopeAPI.updateScope(data.id, data.updateData),
    onSuccess: () => onScopeUpdateSuccess('Scope updated successfully'),
    onError: error =>
      notification.error({
        message: 'Scope update failed',
        description: String(error)
      })
  });

  useEffect(() => {
    if (bidItemName) form.setFieldValue('bidItemName', bidItemName);
  }, [bidItemName]);
  const currentScopeInfo = useMemo(
    () => (scopeId ? scopesList?.find(scope => scope.id === Number(scopeId)) : null),
    [scopesList, scopeId]
  );

  const formattedMasterScopes = useMemo(() => {
    if (!masterScopesData) return [];

    const uniqueScopes = new Map();

    masterScopesData.forEach((scope: MasterScopeDTO) => {
      const name = scope.name || '';
      const division = scope.division || '';
      const uniqueKey = `${division}-${name}`;

      if (name && !uniqueScopes.has(uniqueKey)) {
        uniqueScopes.set(uniqueKey, scope);
      }
    });
    return Array.from(uniqueScopes.values()) as MasterScopeDTO[];
  }, [masterScopesData]);

  // Extract unique divisions from master scopes
  const divisions = useMemo(() => {
    if (!masterScopesData) return [];

    const uniqueDivisions = new Set<string>();
    masterScopesData.forEach(scope => {
      if (scope.division) {
        uniqueDivisions.add(scope.division);
      }
    });
    return Array.from(uniqueDivisions).sort();
  }, [masterScopesData]);

  const handleScopeChange = useCallback(
    (selectedValues: string[]) => {
      const selectedValue = selectedValues[0];
      form.setFieldValue('scope', selectedValue);
      const selectedScope = formattedMasterScopes.find(scope => scope.name === selectedValue);

      if (selectedScope) {
        form.setFieldValue('discipline', selectedScope.discipline);
        form.setFieldValue('division', selectedScope.division);
      }
    },
    [form, formattedMasterScopes]
  );

  useEffect(() => {
    form.setFieldsValue({
      scope: currentScopeInfo?.name,
      discipline: currentScopeInfo?.discipline,
      division: currentScopeInfo?.division
    });
  }, [currentScopeInfo]);

  const handleSave = async () => {
    if (!selectedThumbnailInfo || !selectedThumbnailInfo.sheet || !selectedProjectId || !scopeId)
      return;
    try {
      const values = await form.validateFields();
      if (
        values.scope !== currentScopeInfo?.name ||
        values.discipline !== currentScopeInfo?.discipline ||
        values.division !== currentScopeInfo?.division
      ) {
        const data: ScopeDTO = {
          projectId: Number(selectedProjectId),
          name: values?.scope,
          discipline: values?.discipline,
          division: values?.division
        };
        await updateScopeMutate({ id: Number(scopeId), updateData: data });
        updateReset();
      }
      const updatedItem: BidItemDTO = {
        name: values.bidItemName,
        bidItemSheets: [selectedThumbnailInfo?.sheet],
        projectId: Number(selectedProjectId),
        scopeId: Number(scopeId),
        description: values.notes
      };
      await createBidItem(updatedItem);
      resetCreate();
      onBidItemAdded();
      onClose();
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  if (!scopeId) return null;

  return (
    <Container isThumbnailDrawerMinimized={isThumbnailDrawerMinimized}>
      <LeftPointer />
      <Form form={form} layout='vertical'>
        <Title>Add Bid Item</Title>
        <Form.Item
          label='Bid Item Name'
          name='bidItemName'
          rules={[{ required: true, message: 'Please add bid item name' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label='Scope Name'
          name='scope'
          rules={[{ required: true, message: 'Please select scope' }]}
        >
          <CreatableSearchSelect
            initialOptions={formattedMasterScopes.map(scope => scope.name || '')}
            placeholder='Select or create scope'
            isMultiple={false}
            defaultSelectedValues={currentScopeInfo?.name ? [currentScopeInfo?.name] : []}
            onSelectionChange={handleScopeChange}
          />
        </Form.Item>
        <Form.Item
          label='Division'
          name='division'
          rules={[{ required: true, message: 'Please select division' }]}
        >
          <CreatableSearchSelect
            initialOptions={divisions}
            placeholder='Select division'
            isCreateable={false}
            isMultiple={false}
            defaultSelectedValues={[formValues?.division]}
            onSelectionChange={(selectedValues: string[]) =>
              form.setFieldValue('division', selectedValues[0])
            }
          />
        </Form.Item>
        <Form.Item
          label='Discipline'
          name='discipline'
          rules={[{ required: true, message: 'Please select discipline' }]}
        >
          <CreatableSearchSelect
            initialOptions={disciplines}
            placeholder='Select discipline'
            isCreateable={false}
            isMultiple={false}
            defaultSelectedValues={[formValues?.discipline]}
            onSelectionChange={(selectedValues: string[]) =>
              form.setFieldValue('discipline', selectedValues[0])
            }
          />
        </Form.Item>
        <Form.Item label='Notes' name='notes'>
          <Input />
        </Form.Item>
        <ButtonsContainer>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type='primary'
            onClick={handleSave}
            loading={isPending}
            disabled={isPending || updateStatus === APIMutationStatus.pending}
          >
            Save
          </Button>
        </ButtonsContainer>
      </Form>
    </Container>
  );
};

export default AddBidItem;
