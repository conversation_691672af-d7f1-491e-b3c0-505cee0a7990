import styled from 'styled-components';
import { ThumbnailInfo } from '../utils/thumbnailInfo';

const ImageContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 10px;
  padding-top: 10px;
  height: 100%;
`;

const Image = styled.img`
  width: auto;
  height: 85%;
  min-width: 200px;
  object-fit: contain;
  cursor: pointer;
  border: 1px solid black;
  &: hover {
    border: 6px solid black;
    height: 90%;
  }
  transition: all 0.3s ease-in-out;
`;

interface PagesInfoProps {
  drawings: ThumbnailInfo[];
  specifications: ThumbnailInfo[];
  onSelect: (ThumbnailInfo: ThumbnailInfo | null) => void;
}

const PagesInfo: React.FC<PagesInfoProps> = ({ drawings, specifications, onSelect }) => {
  return (
    <ImageContainer>
      {drawings.length > 0 && (
        <Image
          src={drawings[0].thumbnail}
          alt={drawings[0].sheet.title}
          onClick={() => onSelect(drawings[0])}
        />
      )}
      {specifications.length > 0 && (
        <Image
          src={specifications[0].thumbnail}
          alt={specifications[0].sheet.title}
          onClick={() => onSelect(specifications[0])}
        />
      )}
    </ImageContainer>
  );
};

export default PagesInfo;
