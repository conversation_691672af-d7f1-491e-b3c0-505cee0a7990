import { EditOutlined, SaveOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Form,
  Input,
  Tooltip,
  Popconfirm,
  SelectProps,
  Select,
  App,
  Tag,
  Flex
} from 'antd';
import { ColumnType, TableProps } from 'antd/es/table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FaPlus } from 'react-icons/fa6';
import { useParams } from 'react-router-dom';
import { BidItemDTO, BidItemSheetDTO, BidItemSpecificationDTO } from 'src/api';
import { bidItemAPI, sheetAPI, specificationAPI, scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import ColumnFilter from '../common/columnFilter';
import ExportFile from '../common/exportFile';
import { queryKeys } from '../utils/constant';
import useScopesStore from './store/useScopesStore';
import HasProjectPermission from '../guards/HasProjectPermission';
import { UserProjectPermission } from '../utils/permissions';
import { capitalizeWords } from '../utils/stringUtils';

const ActionButton = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
`;

const StyledTable = styled(Table)<TableProps<BidItemDTO>>`
  scrollbar-width: none;
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  /*Don't have properties in theme config for filter and icons bg color /*
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

interface EditableCellProps {
  editing: boolean;
  dataIndex: keyof BidItemDTO;
  title: string;
  inputType?: 'text';
  record: BidItemDTO;
  index: number;
  children: React.ReactNode;
  selectOptions: { label?: string; value?: number }[];
  editorType?: 'input' | 'select';
  required?: boolean;
}

interface EditableColumnType<RecordType> extends Partial<ColumnType<RecordType>> {
  dataIndex?: keyof BidItemDTO;
  editable?: boolean;
  editorType?: 'input' | 'select';
  selectOptions?: { label?: string; value?: number }[];
  required?: boolean;
}

const StyledSelect = styled(Select)<SelectProps<string>>`
  width: 100%;
`;

const StyledTag = styled(Tag)<{ isSpecification?: boolean }>`
  cursor: pointer;
  background-color: ${themeTokens.tagBlue};
`;

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  editorType,
  selectOptions,
  required,
  ...restProps
}) => {
  const editorNode =
    editorType === 'select' ? (
      <StyledSelect placeholder={`Please select ${title?.toLowerCase()}`} mode='tags'>
        {selectOptions?.map(option => (
          <Select.Option key={option?.value} value={option?.value}>
            {option.label}
          </Select.Option>
        ))}
      </StyledSelect>
    ) : (
      <Input placeholder={`Enter ${title?.toLowerCase()}`} />
    );

  // Create a unique field name for each cell based on row ID and column
  const fieldName = record?.id ? [`${record.id}`, dataIndex] : dataIndex;

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={fieldName}
          style={{ margin: 0 }}
          rules={[{ required, message: `Please enter ${title.toLowerCase()}!` }]}
        >
          {editorNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const ScopeTableView: React.FC<{
  fetchedBidItems?: BidItemDTO[];
  isFetchingBidItems?: boolean;
}> = ({ fetchedBidItems, isFetchingBidItems }) => {
  const [editingItemIds, setEditingItemIds] = useState<number[]>([]);
  const [addItemIds, setAddItemIds] = useState<number[]>([]);
  const [bidItems, setBidItems] = useState<BidItemDTO[]>([]);

  // Use a single form for all rows, but with unique field names for each row
  const [form] = Form.useForm();
  const { scopeId } = useParams();
  const queryClient = useQueryClient();
  const { selectedProjectId, selectedProjectName } = useGlobalStore();
  const {
    isCreatingThumbnails,
    drawingsThumbnails,
    specificationsThumbnails,
    setIsDocumentView,
    setSelectedThumbnailInfo
  } = useScopesStore();
  const { notification } = App.useApp();

  const invalidateBidItemQueries = () => {
    queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] });
    queryClient.invalidateQueries({ queryKey: [queryKeys.currentScopeDrawingsAndSpecs] });
  };

  // Mutations for creating, updating, and deleting bid items
  const { mutate: createBidItem } = useMutation({
    mutationFn: (data: BidItemDTO) => bidItemAPI.createBidItem(data),
    onSuccess: () => invalidateBidItemQueries()
  });

  const { mutate: removeBidItem } = useMutation({
    mutationFn: (id: number) => bidItemAPI.deleteBidItem(id),
    onSuccess: () => invalidateBidItemQueries()
  });

  const { mutate: updateBidItem } = useMutation({
    mutationFn: (args: { id: number; data: BidItemDTO }) =>
      bidItemAPI.updateBidItem(args.id, args.data),
    onSuccess: () => invalidateBidItemQueries()
  });

  const { data: scopeInfo } = useQuery({
    queryKey: [queryKeys.scopeInfo, scopeId],
    queryFn: () => scopeAPI.getScopeById(Number(scopeId)),
    select: res => res.data,
    enabled: !!scopeId
  });

  // Queries for fetching bid items and sheets
  const { data: fetchedSheets } = useQuery({
    queryKey: [queryKeys.allSheets],
    queryFn: () => sheetAPI.getSheetsByProjectId(Number(selectedProjectId)),
    select: res => res.data,
    retry: false
  });

  const { data: specificationsList } = useQuery({
    queryKey: [queryKeys.allSpecifications],
    queryFn: () =>
      specificationAPI.getProjectSpecifications(
        undefined,
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        0,
        1000,
        ['code,asc']
      ),
    select: res => res.data.content,
    retry: false
  });

  useEffect(() => {
    if (fetchedBidItems) {
      const createdItems = bidItems?.filter(
        bidItem => bidItem?.id && addItemIds.includes(bidItem?.id)
      );
      setBidItems([...fetchedBidItems, ...createdItems]);
    }
  }, [fetchedBidItems]);

  useEffect(() => {
    setEditingItemIds([]);
    setBidItems(fetchedBidItems || []);
  }, [scopeId]);

  const isEditing = useCallback(
    (record: BidItemDTO) => {
      if (record?.id) return editingItemIds.includes(record?.id);
      return true;
    },
    [editingItemIds]
  );

  const handleEditItem = useCallback(
    (record: BidItemDTO) => {
      if (!record.id) return;

      // Set the form values with nested field names for this specific row
      const formValues: Record<string, Record<string, string | number[]>> = {};

      formValues[record.id.toString()] = {
        name: record.name || '',
        description: record.description || '',
        bidItemSheets:
          record?.bidItemSheets
            ?.map(sheet => sheet.sheetId)
            .filter((id): id is number => id !== undefined) || [],
        bidItemSpecificationDTOList:
          record?.bidItemSpecificationDTOList
            ?.map(spec => spec.specificationId)
            .filter((id): id is number => id !== undefined) || []
      };

      form.setFieldsValue(formValues);
      setEditingItemIds(prev => [...prev, record.id!]);
    },
    [form]
  );

  const handleDeleteItem = useCallback(
    (id: number) => {
      removeBidItem(id);
    },
    [removeBidItem]
  );

  const handleCancel = useCallback(
    (bidId: number) => {
      // Reset only the fields for this specific row
      form.resetFields([
        [`${bidId}`, 'name'],
        [`${bidId}`, 'bidItemSheets'],
        [`${bidId}`, 'bidItemSpecificationDTOList'],
        [`${bidId}`, 'description']
      ]);

      if (addItemIds.includes(bidId)) {
        setBidItems(prev => {
          return prev.filter(item => item.id !== bidId);
        });

        setAddItemIds(prev => prev.filter(id => id !== bidId));
      } else {
        setEditingItemIds(prev => prev.filter(id => id !== bidId));

        const originalItem = bidItems.find(item => item.id === bidId);
        if (originalItem) {
          const formValues: Record<string, Record<string, string | number[]>> = {};
          formValues[bidId.toString()] = {
            name: originalItem.name || '',
            description: originalItem.description || '',
            bidItemSheets:
              originalItem?.bidItemSheets
                ?.map(sheet => sheet.sheetId)
                .filter((id): id is number => id !== undefined) || [],
            bidItemSpecificationDTOList:
              originalItem?.bidItemSpecificationDTOList
                ?.map(spec => spec.specificationId)
                .filter((id): id is number => id !== undefined) || []
          };
          form.setFieldsValue(formValues);
        }
      }
    },
    [addItemIds, bidItems, form]
  );

  const onSheetClick = useCallback(
    (sheet: BidItemSheetDTO) => {
      if (isCreatingThumbnails) {
        notification.info({
          message: 'Loading thumbnails, please wait...'
        });
        return;
      }
      const thumbnailInfo = drawingsThumbnails.find(
        thumbnail => thumbnail?.sheet?.sheetId === sheet?.sheetId
      );
      if (thumbnailInfo) {
        setSelectedThumbnailInfo(thumbnailInfo);
        setIsDocumentView(true);
      }
    },
    [
      drawingsThumbnails,
      isCreatingThumbnails,
      notification,
      setIsDocumentView,
      setSelectedThumbnailInfo,
      specificationsThumbnails
    ]
  );

  const onSpecsClick = useCallback(
    (specs: BidItemSpecificationDTO) => {
      if (isCreatingThumbnails) {
        notification.info({
          message: 'Loading thumbnails, please wait...'
        });
        return;
      }
      const thumbnailInfo = specificationsThumbnails.find(
        thumbnail => thumbnail?.specification?.specificationId === specs?.specificationId
      );
      if (thumbnailInfo) {
        setSelectedThumbnailInfo(thumbnailInfo);
        setIsDocumentView(true);
      }
    },
    [
      drawingsThumbnails,
      isCreatingThumbnails,
      notification,
      setIsDocumentView,
      setSelectedThumbnailInfo,
      specificationsThumbnails
    ]
  );

  const handleSaveItem = useCallback(
    async (bidId: number) => {
      try {
        const values = await form.validateFields([
          [`${bidId}`, 'name'],
          [`${bidId}`, 'bidItemSheets'],
          [`${bidId}`, 'bidItemSpecificationDTOList'],
          [`${bidId}`, 'description']
        ]);
        const rowData = values[bidId];

        if (!rowData) {
          throw new Error('No data found for this row');
        }

        const updatedItem: BidItemDTO = {
          ...rowData,
          id: bidId,
          bidItemSheets: rowData?.bidItemSheets?.map((sheetId: number) => ({ sheetId })) || [],
          bidItemSpecificationDTOList:
            rowData?.bidItemSpecificationDTOList?.map((specificationId: number) => ({
              specificationId
            })) || [],
          projectId: Number(selectedProjectId),
          scopeId: Number(scopeId)
        };

        setBidItems(prev => {
          const updatedItems = [...prev];
          const itemIndex = updatedItems.findIndex(item => item.id === bidId);

          if (itemIndex !== -1) {
            updatedItems[itemIndex] = {
              ...updatedItems[itemIndex],
              ...updatedItem
            };
          }

          return updatedItems;
        });
        if (addItemIds.includes(bidId)) {
          createBidItem({ ...updatedItem });
        } else {
          updateBidItem({ id: bidId, data: updatedItem });
        }

        setEditingItemIds(prev => prev.filter(id => id !== bidId));
        if (addItemIds.includes(bidId)) {
          setAddItemIds(prev => prev.filter(id => id !== bidId));
        }
      } catch {
        notification.error({
          message: 'Validation Failed',
          description: 'Please check the form fields and try again.'
        });
      }
    },
    [addItemIds, createBidItem, form, notification, scopeId, selectedProjectId, updateBidItem]
  );

  const handleAddBidItem = useCallback(() => {
    const newId = Math.floor(Math.random() * 1000000);

    const newBidItem: BidItemDTO = {
      id: newId,
      name: '',
      description: ''
    };

    // Create form values with the new item's ID
    const formValues: Record<string, Record<string, string | number[]>> = {};
    formValues[newId.toString()] = {
      name: '',
      bidItemSheets: [],
      bidItemSpecificationDTOList: [],
      description: ''
    };
    form.setFieldsValue(formValues);

    setBidItems(prevItems => [...prevItems, newBidItem]);
    setEditingItemIds(prev => [...prev, newId]);
    setAddItemIds(prev => [...prev, newId]);
  }, [form]);

  const columns = useMemo(
    () =>
      [
        {
          title: 'Item #',
          width: 90,
          dataIndex: 'id',
          required: true,
          render: (_, _record, index) => {
            return index + 1;
          }
        },
        {
          title: 'Bid Item Description',
          dataIndex: 'name',
          editable: true,
          width: 210,
          required: true,
          sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
          ...ColumnFilter('name')
        },
        {
          title: 'Drawing Reference',
          dataIndex: 'bidItemSheets',
          width: 200,
          editable: true,
          editorType: 'select',
          required: false,
          selectOptions: fetchedSheets
            ? fetchedSheets?.map(item => ({
                value: item?.id,
                label: `${item?.sheetNumber} - ${capitalizeWords(item?.title)}`
              }))
            : [],
          filters: fetchedSheets
            ? fetchedSheets?.map(sheet => ({
                text: `${sheet?.sheetNumber} - ${capitalizeWords(sheet?.title)}`,
                value: sheet?.id || 0
              }))
            : [],
          onFilter: (value: number | boolean, record: BidItemDTO) => {
            return (
              record?.bidItemSheets?.some((sheet: BidItemSheetDTO) => sheet.sheetId === value) ||
              false
            );
          },
          sorter: (a, b) => (a?.bidItemSheets?.length || 0) - (b?.bidItemSheets?.length || 0),
          render: (_, record) => {
            return (
              <>
                {record?.bidItemSheets?.map((sheet: BidItemSheetDTO) =>
                  sheet && sheet.sheetNumber && sheet.title ? (
                    <StyledTag key={sheet.sheetId} onClick={() => onSheetClick(sheet)}>
                      {`${sheet.sheetNumber} - ${capitalizeWords(sheet.title)}`}
                    </StyledTag>
                  ) : null
                )}
              </>
            );
          }
        },
        {
          title: 'Specification Reference',
          dataIndex: 'bidItemSpecificationDTOList',
          width: 440,
          editable: true,
          required: false,
          editorType: 'select',
          selectOptions: specificationsList
            ? specificationsList?.map(item => ({
                value: item.id,
                label: `${item.code} - ${capitalizeWords(item.title)}`
              }))
            : [],
          filters: specificationsList
            ? specificationsList?.map(item => ({
                value: item.id || 0,
                text: `${item.code} - ${capitalizeWords(item.title)}`
              }))
            : [],
          onFilter: (value: number | boolean, record: BidItemDTO) => {
            return (
              record?.bidItemSpecificationDTOList?.some(
                (spec: BidItemSpecificationDTO) => spec.specificationId === value
              ) || false
            );
          },
          sorter: (a, b) =>
            (a?.bidItemSpecificationDTOList?.length || 0) -
            (b?.bidItemSpecificationDTOList?.length || 0),
          render: (_, record) => {
            return (
              <Flex gap={4} wrap>
                {record?.bidItemSpecificationDTOList?.map((spec: BidItemSpecificationDTO) =>
                  spec && spec.specificationCode ? (
                    <StyledTag
                      key={spec.specificationId}
                      isSpecification={true}
                      onClick={() => onSpecsClick(spec)}
                    >
                      {`${spec.specificationCode} - ${capitalizeWords(spec.specificationTitle)}`}
                    </StyledTag>
                  ) : null
                )}
              </Flex>
            );
          }
        },
        {
          title: 'Notes',
          required: false,
          width: 150,
          dataIndex: 'description',
          editable: true,
          editorType: 'input',
          sorter: (a, b) => (a.description || '').localeCompare(b.description || ''),
          ...ColumnFilter('description')
        },
        {
          title: 'Actions',
          key: 'edit',
          width: 120,
          render: (_, record) => {
            const editable = isEditing(record);

            return (
              <>
                {editable ? (
                  <>
                    <Tooltip trigger={'hover'} title='Save'>
                      <SaveOutlined
                        onClick={() => record.id && handleSaveItem(record.id)}
                        style={{ marginRight: 8 }}
                      />
                    </Tooltip>
                    <Tooltip trigger={'hover'} title='Cancel'>
                      <CloseOutlined onClick={() => record.id && handleCancel(record?.id)} />
                    </Tooltip>
                  </>
                ) : (
                  <>
                    <HasProjectPermission
                      requiredPermissions={[UserProjectPermission.EDIT_BID_ITEM]}
                    >
                      <Tooltip trigger={'hover'} title='Edit'>
                        <EditOutlined
                          className='edit-icon'
                          onClick={() => handleEditItem(record)}
                          style={{ marginRight: 8 }}
                        />
                      </Tooltip>
                    </HasProjectPermission>
                    <HasProjectPermission
                      requiredPermissions={[UserProjectPermission.DELETE_BID_ITEM]}
                    >
                      <Tooltip trigger={'hover'} title='Delete'>
                        <Popconfirm
                          title='Are you sure to delete this Bid Item?'
                          onConfirm={() => record.id && handleDeleteItem(record.id)}
                          onCancel={() => null}
                          okText='Yes'
                          placement='left'
                          cancelText='No'
                        >
                          <DeleteOutlined className='edit-icon' />
                        </Popconfirm>
                      </Tooltip>
                    </HasProjectPermission>
                  </>
                )}
              </>
            );
          }
        }
      ] as EditableColumnType<BidItemDTO>[],
    [
      fetchedSheets,
      handleCancel,
      handleDeleteItem,
      handleEditItem,
      handleSaveItem,
      isEditing,
      onSheetClick,
      onSpecsClick,
      specificationsList
    ]
  );

  const mergedColumns = useMemo(
    () =>
      columns?.map(col => {
        if (!col.editable) {
          return col;
        }
        return {
          ...col,
          onCell: (record: BidItemDTO) => ({
            record,
            inputType: 'text',
            dataIndex: col.dataIndex,
            title: String(col.title),
            editing: isEditing(record),
            editorType: col.editorType,
            selectOptions: col.selectOptions,
            required: col.required
          })
        };
      }) as EditableColumnType<BidItemDTO>[],
    [columns, isEditing]
  );

  const exportColumns = [
    { label: 'Item #', key: 'itemNo' },
    { label: 'Bid Item Description', key: 'name' },
    { label: 'Drawing Reference', key: 'bidItemSheets' },
    { label: 'Specification Reference', key: 'bidItemSpecifications' },
    { label: 'Notes', key: 'description' }
  ];

  return (
    <>
      <ActionButton>
        <HasProjectPermission requiredPermissions={[UserProjectPermission.CREATE_BID_ITEM]}>
          <Button onClick={handleAddBidItem} icon={<FaPlus size={16} />} type='primary'>
            Bid Item
          </Button>
        </HasProjectPermission>
        <ExportFile
          data={bidItems?.map((bidItem: BidItemDTO, index: number) => ({
            ...bidItem,
            itemNo: String(index + 1),
            bidItemSheets: bidItem.bidItemSheets
              ?.map(sheet => `${sheet.sheetNumber} - ${capitalizeWords(sheet.title)}`)
              .join(','),
            bidItemSpecifications: bidItem.bidItemSpecificationDTOList
              ?.map(
                spec => `${spec.specificationCode} - ${capitalizeWords(spec.specificationTitle)}`
              )
              .join(',')
          }))}
          columns={exportColumns}
          filename={`${scopeInfo?.scopeNameWithSpecCode || scopeInfo?.name || ''}_BidItems`}
          projectName={selectedProjectName || ''}
          scopeName={scopeInfo?.scopeNameWithSpecCode || scopeInfo?.name || ''}
        />
      </ActionButton>
      <Form form={form} component={false}>
        <StyledTable
          components={{
            body: {
              cell: EditableCell
            }
          }}
          rowClassName='editable-row'
          bordered
          loading={isFetchingBidItems}
          dataSource={bidItems}
          columns={mergedColumns}
          pagination={false}
          rowHoverable
          onRow={() => ({
            className: 'editable-row'
          })}
          scroll={{ y: 'calc(100vh - 300px)', x: true }}
        />
      </Form>
    </>
  );
};

export default ScopeTableView;
