import { EditOutlined, SaveOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  Button,
  Form,
  Input,
  Tooltip,
  Popconfirm,
  SelectProps,
  Select,
  App,
  Tag,
  Flex
} from 'antd';
import { ColumnType, TableProps } from 'antd/es/table';
import { Key, useCallback, useEffect, useMemo, useState } from 'react';
import { FaPlus } from 'react-icons/fa6';
import { useParams } from 'react-router-dom';
import { BidItemDTO, BidItemSheetDTO, BidItemSpecificationDTO } from 'src/api';
import { bidItemAPI, sheetAPI, specificationAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import ColumnFilter from '../common/columnFilter';
import ExportFile from '../common/exportFile';
import { queryKeys } from '../utils/constant';
import useScopesStore from './store/useScopesStore';
import HasProjectPermission from '../guards/HasProjectPermission';
import { UserProjectPermission } from '../utils/permissions';
import { capitalizeWords } from '../utils/stringUtils';

const ActionButton = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
`;

const StyledTable = styled(Table)<TableProps<BidItemDTO>>`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  /*Don't have properties in theme config for filter and icons bg color /*
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

interface EditableCellProps {
  editing: boolean;
  dataIndex: keyof BidItemDTO;
  title: string;
  inputType?: 'text';
  record: BidItemDTO;
  index: number;
  children: React.ReactNode;
  selectOptions: { label?: string; value?: number }[];
  editorType?: 'input' | 'select';
  required?: boolean;
}

interface EditableColumnType<RecordType> extends ColumnType<RecordType> {
  dataIndex?: keyof BidItemDTO;
  editable?: boolean;
  editorType?: 'input' | 'select';
  selectOptions?: { label?: string; value?: number }[];
  filters?: { text: string; value: number }[];
  onFilter?: (value: boolean | Key, record: RecordType) => boolean;
  filterIcon?: React.ReactNode;
  filterDropdown?: React.ReactNode;
  required?: boolean;
}

const StyledSelect = styled(Select)<SelectProps<string>>`
  width: 100%;
`;

const StyledTag = styled(Tag)<{ isSpecification?: boolean }>`
  cursor: ${({ isSpecification }) => (isSpecification ? 'default' : 'pointer')};
  background-color: ${themeTokens.tagBlue};
`;

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  editorType,
  selectOptions,
  required,
  ...restProps
}) => {
  const editorNode =
    editorType === 'select' ? (
      <StyledSelect placeholder={`Please select ${title?.toLowerCase()}`} mode='tags'>
        {selectOptions?.map(option => (
          <Select.Option key={option?.value} value={option?.value}>
            {option.label}
          </Select.Option>
        ))}
      </StyledSelect>
    ) : (
      <Input placeholder={`Enter ${title?.toLowerCase()}`} />
    );

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[{ required, message: `Please enter ${title.toLowerCase()}!` }]}
        >
          {editorNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const ScopeTableView: React.FC<{ fetchedBidItems?: BidItemDTO[] }> = ({ fetchedBidItems }) => {
  const [editingItemId, setEditingItemId] = useState<number>();
  const [bidItems, setBidItems] = useState<BidItemDTO[]>([]);
  const [hoveredRowKey, setHoveredRowKey] = useState<number | null>(null);
  const [isAddingItem, setIsAddingItem] = useState<boolean>(false);

  const [form] = Form.useForm();
  const { scopeId } = useParams();
  const queryClient = useQueryClient();
  const { selectedProjectId } = useGlobalStore();
  const {
    isCreatingThumbnails,
    drawingsThumbnails,
    specificationsThumbnails,
    setIsDocumentView,
    setSelectedThumbnailInfo
  } = useScopesStore();
  const { notification } = App.useApp();

  // Mutations for creating, updating, and deleting bid items
  const { mutate: createBidItem, reset: resetCreate } = useMutation({
    mutationFn: (data: BidItemDTO) => bidItemAPI.createBidItem(data),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] })
  });

  const { mutate: removeBidItem } = useMutation({
    mutationFn: (id: number) => bidItemAPI.deleteBidItem(id),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] })
  });

  const { mutate: updateBidItem } = useMutation({
    mutationFn: (args: { id: number; data: BidItemDTO }) =>
      bidItemAPI.updateBidItem(args.id, args.data),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: [queryKeys.allBidItems] })
  });

  // Queries for fetching bid items and sheets
  const { data: fetchedSheets } = useQuery({
    queryKey: [queryKeys.allSheets],
    queryFn: () => sheetAPI.getSheetsByProjectId(Number(selectedProjectId)),
    select: res => res.data,
    retry: false
  });

  const { data: specificationsList } = useQuery({
    queryKey: [queryKeys.allSpecifications],
    queryFn: () =>
      specificationAPI.getProjectSpecifications(
        undefined,
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        0,
        1000,
        ['code,asc']
      ),
    select: res => res.data.content,
    retry: false
  });

  useEffect(() => {
    if (fetchedBidItems) {
      setBidItems(fetchedBidItems);
    }
  }, [fetchedBidItems]);

  useEffect(() => {
    setIsAddingItem(false);
    setEditingItemId(undefined);
  }, [scopeId]);

  const handleMouseEnter = (id?: number) => {
    if (!id) return;
    setHoveredRowKey(id);
  };

  const handleMouseLeave = () => {
    setHoveredRowKey(null);
  };

  const isEditing = useCallback(
    (record: BidItemDTO) => record?.id && record?.id === editingItemId,
    [editingItemId]
  );

  const handleEditItem = useCallback(
    (record: BidItemDTO) => {
      console.log('enter into handleEditItem', editingItemId, isAddingItem);

      if (editingItemId && isAddingItem) {
        console.log('enter into handleEditItem in if');
        const filteredBidItems = bidItems.filter(item => item.id !== editingItemId);
        setBidItems(filteredBidItems);
        setIsAddingItem(false);
      }
      setIsAddingItem(false);
      form.setFieldsValue({
        ...record,
        bidItemSheets: record?.bidItemSheets?.map(sheet => sheet.sheetId) || [],
        bidItemSpecificationDTOList:
          record?.bidItemSpecificationDTOList?.map(spec => spec.specificationId) || []
      });
      setEditingItemId(record.id);
    },
    [bidItems, editingItemId, form, isAddingItem]
  );

  const handleDeleteItem = useCallback(
    (id: number) => {
      removeBidItem(id);
    },
    [removeBidItem]
  );

  const handleCancel = useCallback(() => {
    form.resetFields();
    resetCreate();
    if (isAddingItem && editingItemId) {
      const filteredBidItems = bidItems.filter(item => item.id !== editingItemId);
      setBidItems(filteredBidItems);
      setIsAddingItem(false);
    }

    setEditingItemId(undefined);
  }, [bidItems, editingItemId, form, isAddingItem, resetCreate]);

  const onSheetClick = useCallback(
    (sheet: BidItemSheetDTO) => {
      if (isCreatingThumbnails) {
        notification.info({
          message: 'Please wait while thumbnails are being created'
        });
        return;
      }
      const thumbnailInfo =
        drawingsThumbnails.find(thumbnail => thumbnail.sheet.sheetId === sheet.sheetId) ||
        specificationsThumbnails.find(thumbnail => thumbnail.sheet.sheetId === sheet.sheetId);
      if (thumbnailInfo) {
        setSelectedThumbnailInfo(thumbnailInfo);
        setIsDocumentView(true);
      }
    },
    [drawingsThumbnails, isCreatingThumbnails, specificationsThumbnails]
  );

  const handleSaveItem = useCallback(
    async (bidId: number) => {
      try {
        const data = await form.validateFields();
        const updatedItem: BidItemDTO = {
          ...data,
          bidItemSheets: data?.bidItemSheets?.map((sheetId: number) => ({ sheetId })) || [],
          bidItemSpecificationDTOList:
            data?.bidItemSpecificationDTOList?.map((specificationId: number) => ({
              specificationId
            })) || [],
          projectId: Number(selectedProjectId),
          scopeId: Number(scopeId)
        };
        if (isAddingItem) {
          createBidItem({ ...updatedItem });
          setIsAddingItem(false);
        } else {
          updateBidItem({ id: bidId, data: updatedItem });
        }
        setEditingItemId(undefined);
      } catch (error) {
        console.error('Validation Failed:', error);
      }
    },
    [createBidItem, form, isAddingItem, scopeId, selectedProjectId, updateBidItem]
  );

  const handleAddBidItem = useCallback(() => {
    setIsAddingItem(true);
    const newBidItem: BidItemDTO = {
      id: Math.floor(Math.random() * 1000000),
      name: '',
      description: ''
    };
    form.setFieldsValue({
      ...newBidItem,
      name: newBidItem.name || undefined,
      bidItemSheets: newBidItem.bidItemSheets || [],
      specifications: newBidItem.description || undefined,
      notes: newBidItem.description || undefined
    });
    setBidItems(prevItems => [...prevItems, newBidItem]);
    setEditingItemId(newBidItem.id);
  }, [form]);

  const columns: EditableColumnType<BidItemDTO>[] = useMemo(
    () => [
      {
        title: 'Bid Items',
        dataIndex: 'name',
        editable: true,
        required: true,
        sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
        ...ColumnFilter('name')
      },
      {
        title: 'Sheets',
        dataIndex: 'bidItemSheets',
        editable: true,
        editorType: 'select',
        required: false,
        selectOptions: fetchedSheets
          ? fetchedSheets?.map(item => ({ value: item.id, label: item.sheetNumber }))
          : [],
        filters: fetchedSheets
          ? fetchedSheets.map(sheet => ({ text: sheet.sheetNumber, value: sheet.id }))
          : [],
        onFilter: (value, record) => {
          return record?.bidItemSheets?.some(sheet => sheet.sheetId === value);
        },
        sorter: (a, b) => (a?.bidItemSheets?.length || 0) - (b?.bidItemSheets?.length || 0),
        render: (_, record) => {
          return (
            <>
              {record?.bidItemSheets?.map((sheet: BidItemSheetDTO) => (
                <StyledTag key={sheet.sheetId} onClick={() => onSheetClick(sheet)}>
                  {sheet.sheetNumber}
                </StyledTag>
              ))}
            </>
          );
        }
      },
      {
        title: 'Specifications',
        dataIndex: 'bidItemSpecificationDTOList',
        editable: true,
        required: false,
        editorType: 'select',
        selectOptions: specificationsList
          ? specificationsList?.map(item => ({
              value: item.id,
              label: `${item.code} - ${item.title}`
            }))
          : [],
        filters: specificationsList
          ? specificationsList.map(item => ({
              value: item.id,
              text: `${item.code} - ${item.title}`
            }))
          : [],
        onFilter: (value, record) => {
          return record?.bidItemSpecificationDTOList?.some(spec => spec.specificationId === value);
        },
        sorter: (a, b) =>
          (a?.bidItemSpecificationDTOList?.length || 0) -
          (b?.bidItemSpecificationDTOList?.length || 0),
        render: (_, record) => {
          return (
            <Flex gap={4} wrap>
              {record?.bidItemSpecificationDTOList?.map((sheet: BidItemSpecificationDTO) => (
                <StyledTag key={sheet.specificationId} isSpecification={true}>
                  {`${sheet.specificationCode} - ${capitalizeWords(sheet.specificationTitle)}`}
                </StyledTag>
              ))}
            </Flex>
          );
        }
      },
      {
        title: 'Notes',
        required: false,
        dataIndex: 'description',
        editable: true,
        editorType: 'input',
        sorter: (a, b) => (a.description || '').localeCompare(b.description || ''),
        ...ColumnFilter('description')
      },
      {
        title: 'Actions',
        key: 'edit',
        width: 120,
        render: (_, record) => {
          const editable = isEditing(record);
          if (!record?.id) return null;

          return (
            <>
              {editable ? (
                <>
                  <Tooltip trigger={'hover'} title='Save'>
                    <SaveOutlined
                      onClick={() => record.id && handleSaveItem(record.id)}
                      style={{ marginRight: 8 }}
                    />
                  </Tooltip>
                  <Tooltip trigger={'hover'} title='Cancel'>
                    <CloseOutlined onClick={handleCancel} />
                  </Tooltip>
                </>
              ) : (
                <>
                  <HasProjectPermission requiredPermissions={[UserProjectPermission.EDIT_BID_ITEM]}>
                    <Tooltip trigger={'hover'} title='Edit'>
                      <EditOutlined
                        className='edit-icon'
                        onClick={() => handleEditItem(record)}
                        style={{ marginRight: 8 }}
                      />
                    </Tooltip>
                  </HasProjectPermission>
                  <HasProjectPermission
                    requiredPermissions={[UserProjectPermission.DELETE_BID_ITEM]}
                  >
                    <Tooltip trigger={'hover'} title='Delete'>
                      <Popconfirm
                        title='Are you sure to delete this Bid Item?'
                        onConfirm={() => record.id && handleDeleteItem(record.id)}
                        onCancel={() => null}
                        okText='Yes'
                        placement='left'
                        cancelText='No'
                      >
                        <DeleteOutlined className='edit-icon' />
                      </Popconfirm>
                    </Tooltip>
                  </HasProjectPermission>
                </>
              )}
            </>
          );
        }
      }
    ],
    [
      fetchedSheets,
      handleCancel,
      handleDeleteItem,
      handleEditItem,
      handleSaveItem,
      isEditing,
      onSheetClick,
      specificationsList
    ]
  );

  const mergedColumns = useMemo(
    () =>
      columns.map(col => {
        if (!col.editable) {
          return col;
        }
        return {
          ...col,
          onCell: (record: BidItemDTO) => ({
            record,
            inputType: 'text',
            dataIndex: col.dataIndex,
            title: String(col.title),
            editing: isEditing(record),
            editorType: col.editorType,
            selectOptions: col.selectOptions,
            required: col.required
          })
        };
      }),
    [columns, isEditing]
  );

  const exportColumns = [
    { label: 'Bid Items', key: 'name' },
    { label: 'Sheets', key: 'bidItemSheets' },
    { label: 'Notes', key: 'description' }
  ];

  return (
    <>
      <ActionButton>
        <ExportFile
          data={bidItems.map(bidItem => ({
            ...bidItem,
            bidItemSheets: bidItem.bidItemSheets?.map(sheet => sheet.sheetNumber).join(',')
          }))}
          columns={exportColumns}
          filename='bidItems'
        />
      </ActionButton>
      <Form form={form} component={false}>
        <StyledTable
          components={{
            body: {
              cell: EditableCell
            }
          }}
          rowClassName='editable-row'
          bordered
          dataSource={bidItems}
          columns={mergedColumns}
          pagination={false}
          rowHoverable
          onRow={record => ({
            onMouseEnter: () => handleMouseEnter(record?.id),
            onMouseLeave: handleMouseLeave,
            className: 'editable-row'
          })}
        />
      </Form>
      <HasProjectPermission requiredPermissions={[UserProjectPermission.CREATE_BID_ITEM]}>
        <ActionButton>
          <Button
            onClick={handleAddBidItem}
            icon={<FaPlus size={20} />}
            disabled={isAddingItem}
            type='primary'
          >
            Add Bid Item
          </Button>
        </ActionButton>
      </HasProjectPermission>
    </>
  );
};

export default ScopeTableView;
