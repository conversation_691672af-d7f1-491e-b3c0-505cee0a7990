import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { Outlet, useNavigate, useParams } from 'react-router-dom';
import { scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import DocumentProcessingIndicator from './DocumentProcessingIndicator';
import NoScopeComponent from './noScope';
import { DocProcessingJobStatus, queryKeys } from '../utils/constant';

const Scopes = () => {
  const { scopeId } = useParams();
  const navigate = useNavigate();
  const { selectedProjectId, documentProcessingListeners, selectedVersion } = useGlobalStore();

  const {
    data: scopesList,
    isPending,
    refetch
  } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        undefined,
        0,
        10,
        ['division,asc']
      ),
    select: res => res.data,
    enabled: !!selectedProjectId,
    retry: false
  });

  const documentsProcessingStatus = useMemo(() => {
    if (documentProcessingListeners && selectedProjectId) {
      return documentProcessingListeners[Number(selectedProjectId)];
    }
    return null;
  }, [documentProcessingListeners, selectedProjectId]);

  useEffect(() => {
    if (!scopeId && scopesList?.content && scopesList?.content?.length > 0) {
      navigate(String(scopesList.content[0]?.id));
    }
  }, [scopeId, scopesList]);

  useEffect(() => {
    if (documentsProcessingStatus === DocProcessingJobStatus.completed) refetch();
  }, [documentsProcessingStatus]);

  if (isPending) {
    return null;
  }

  if (documentsProcessingStatus === DocProcessingJobStatus.inProgress) {
    return <DocumentProcessingIndicator />;
  }

  return (
    <>{scopesList?.content && scopesList?.content.length > 0 ? <Outlet /> : <NoScopeComponent />}</>
  );
};

export default Scopes;
