import { ConfigProvider } from 'antd';
import React, { ReactNode } from 'react';
import { themeTokens } from '../../theme/tokens';

interface SubSiderThemeProviderProps {
  children: ReactNode;
}

const SubSiderThemeProvider: React.FC<SubSiderThemeProviderProps> = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            siderBg: themeTokens.subSiderBg,
            triggerBg: themeTokens.subSiderBg
          },
          Menu: {
            itemSelectedColor: themeTokens.textLight,
            itemSelectedBg: themeTokens.menuSubMenuItemSelectedBg,
            itemHoverBg: themeTokens.menuSubMenuItemHoverBg,
            itemHoverColor: themeTokens.textLight,
            itemColor: themeTokens.textLight,
            itemBg: themeTokens.subSiderBg,
            fontSize: 18
          }
        }
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default SubSiderThemeProvider;
