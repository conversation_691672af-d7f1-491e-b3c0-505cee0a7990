import { useQuery } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { scopeAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import noScopesFoundIcon from '../../assets/images/noScopesFoundIcon.svg';
import UploadDocuments from '../projectDocuments/uploadDocuments';
import { queryKeys } from '../utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 50px;
  height: 100%;
  overflow: hidden scroll;
  scrollbar-width: none;
`;

const Header = styled.div`
  font-family: inter;
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const Description = styled.div`
  font-weight: 300;
  font-size: 27px;
  margin: 0;
  font-family: inter;
  color: #4e4e4e;
`;

const NoScopeComponent = () => {
  const { scopeId } = useParams();
  const navigate = useNavigate();
  const { selectedProjectId, selectedVersion } = useGlobalStore();
  const [isUploadDocumentModalOpen, setIsUploadDocumentModalOpen] = useState(false);

  const { data: scopesList } = useQuery({
    queryKey: [queryKeys.allScopes, selectedProjectId],
    queryFn: () =>
      scopeAPI.getScopes(
        Number(selectedProjectId),
        undefined,
        undefined,
        undefined,
        undefined,
        0,
        10,
        ['division,asc']
      ),
    select: res => res.data,
    enabled: !!selectedProjectId
  });

  useEffect(() => {
    if (!scopeId && scopesList?.content && scopesList?.content?.length > 0) {
      navigate(String(scopesList.content[0]?.id));
    }
  }, [scopeId, scopesList]);

  return (
    <Container>
      <Flex vertical align='center' justify='center' gap={60}>
        <Header>No scopes found</Header>
        <Flex vertical align='center' justify='center' gap={32}>
          <img src={noScopesFoundIcon} alt='No scopes found' />
          <Description>Please upload documents</Description>
        </Flex>
      </Flex>
      <UploadDocuments
        isDocumentsScreen={false}
        isUploadDocumentModalOpen={isUploadDocumentModalOpen}
        setIsUploadDocumentModalOpen={setIsUploadDocumentModalOpen}
      />
    </Container>
  );
};

export default NoScopeComponent;
