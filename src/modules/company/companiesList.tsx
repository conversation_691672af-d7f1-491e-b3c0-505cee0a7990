import { SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  Avatar,
  Button,
  Card,
  Dropdown,
  Tooltip,
  Empty,
  Flex,
  Form,
  Input,
  List,
  Pagination,
  Select
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { FaChevronRight } from 'react-icons/fa';
import { FaPlus } from 'react-icons/fa6';
import { LiaSlidersHSolid } from 'react-icons/lia';
import { LuArrowDownUp } from 'react-icons/lu';
import { Link } from 'react-router-dom';
import { CompanyDTO, PageCompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import AddCompany from './addCompany';
import CustomIcon from '../common/customIcon';
import { queryKeys, subscriptionStatusOptions } from '../utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  padding: 48px 0;
`;

const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 24px;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  background: ${themeTokens.pageBg};
  padding: 20px 0;
`;

const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

const StyledCard = styled(Card)`
  width: 100%;
`;

const StyledAvatar = styled(Avatar)`
  background: #ffffff;
  color: black;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
`;

const CompanyTitle = styled.div`
  font-weight: 400;
  font-size: 24px;
  line-height: 28px;
`;

const CompanyName = styled.span`
  font-weight: 700;
`;

const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;

interface FilterState {
  industry?: string;
  subscriptionStatus?: 'ACTIVE' | 'TRIAL' | 'SUSPENDED';
}

const CompaniesList: React.FC = () => {
  const [isAddCompanyModalOpen, setIsAddCompanyModalOpen] = useState<boolean>(false);
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [filters, setFilters] = useState<FilterState>({});
  const [isSortAscending, setIsSortAscending] = useState<boolean>(false);

  const [filterForm] = Form.useForm();

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // API Queries
  const { data: companiesList, isPending } = useQuery({
    queryKey: [
      queryKeys.companiesList,
      currentPage,
      pageSize,
      debouncedSearchText,
      filters.industry,
      filters.subscriptionStatus,
      isSortAscending
    ],
    queryFn: () =>
      companyAPI.getCompanies(
        currentPage,
        pageSize,
        ['createdAt', isSortAscending ? 'asc' : 'desc'],
        debouncedSearchText,
        filters.industry,
        filters.subscriptionStatus
      ),
    select: res => {
      if (res.data && typeof res.data === 'object' && 'content' in res.data) {
        return res.data as PageCompanyDTO;
      }
      return { content: [], totalElements: 0 } as PageCompanyDTO;
    },
    staleTime: 30000 // 30 seconds
  });

  const handleAddCompany = useCallback(() => setIsAddCompanyModalOpen(true), []);
  const onAddCompanyModalClose = useCallback(() => setIsAddCompanyModalOpen(false), []);

  const handleFilterReset = useCallback(() => {
    filterForm.resetFields();
    setFilters({});
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const handleFilterApply = useCallback(() => {
    const values = filterForm.getFieldsValue();
    setFilters(values);
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const onSearchHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    setCurrentPage(0);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page - 1);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  const FilterFormContent = () => (
    <Form form={filterForm} layout='vertical' initialValues={filters}>
      <Form.Item label='Industry' name='industry'>
        <Input placeholder='Enter industry' />
      </Form.Item>
      <Form.Item label='Subscription Status' name='subscriptionStatus'>
        <Select placeholder='Select Status' allowClear options={subscriptionStatusOptions} />
      </Form.Item>
      <Flex justify='space-between' align='center'>
        <Button onClick={handleFilterReset}>Reset</Button>
        <Button type='primary' onClick={handleFilterApply}>
          Apply Filters
        </Button>
      </Flex>
    </Form>
  );

  return (
    <Container>
      <Content>
        <HeaderContainer>
          <Header>Companies</Header>
          <Flex gap={24} justify='flex-end' align='center'>
            <StyledInput
              size='large'
              placeholder='Search here'
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={onSearchHandler}
            />
            <Tooltip title='Sort'>
              <LuArrowDownUp
                cursor='pointer'
                onClick={() => setIsSortAscending(!isSortAscending)}
                size={20}
              />
            </Tooltip>
            <Dropdown
              trigger={['click']}
              placement='bottomRight'
              open={isFilterDropdownOpen}
              onOpenChange={setIsFilterDropdownOpen}
              dropdownRender={() => (
                <FilterDropdownContainer>
                  <Flex vertical gap={16}>
                    <h3>Filter Companies</h3>
                    <FilterFormContent />
                  </Flex>
                </FilterDropdownContainer>
              )}
            >
              <LiaSlidersHSolid cursor='pointer' size={22} />
            </Dropdown>
            <StyledButton
              onClick={handleAddCompany}
              icon={<CustomIcon Icon={FaPlus} />}
              type='primary'
            >
              Add Company
            </StyledButton>
          </Flex>
        </HeaderContainer>
        <List
          grid={{
            gutter: 25,
            xs: 1,
            sm: 1,
            md: 2,
            lg: 2,
            xl: 2,
            xxl: 2
          }}
          loading={isPending}
          dataSource={(companiesList?.content || []) as CompanyDTO[]}
          locale={{ emptyText: <Empty description='No companies found' /> }}
          renderItem={item => (
            <List.Item>
              <StyledCard>
                <Flex align='center' justify='space-between'>
                  <Flex gap={8} vertical>
                    <CompanyTitle>
                      <CompanyName>{item?.name}</CompanyName>
                    </CompanyTitle>
                  </Flex>
                  <Link to={`${item?.id}`}>
                    <StyledAvatar icon={<CustomIcon Icon={FaChevronRight} />} />
                  </Link>
                </Flex>
              </StyledCard>
            </List.Item>
          )}
        />
        {companiesList && companiesList.totalElements && companiesList.totalElements > 0 ? (
          <Flex justify='center'>
            <Pagination
              current={currentPage + 1}
              pageSize={pageSize}
              total={companiesList.totalElements}
              showSizeChanger
              pageSizeOptions={['5', '10', '20', '50']}
              onChange={handlePageChange}
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} companies`}
            />
          </Flex>
        ) : null}
      </Content>
      {isAddCompanyModalOpen && (
        <AddCompany
          open={isAddCompanyModalOpen}
          onClose={onAddCompanyModalClose}
          editMode={false}
        />
      )}
    </Container>
  );
};

export default CompaniesList;
