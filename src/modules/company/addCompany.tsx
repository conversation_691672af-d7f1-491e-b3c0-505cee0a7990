import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { App, Form, Input, Modal, Select } from 'antd';
import { isEqual } from 'lodash';
import { useEffect, useMemo } from 'react';
import { CompanyDTO } from 'src/api';
import { companyAPI, companySubscriptionAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import { APIMutationStatus, queryKeys, subscriptionStatusOptions } from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';

type AddCompanyProps = {
  open: boolean;
  onClose: () => void;
  selectedCompany?: CompanyDTO;
  editMode: boolean;
};

const StyledForm = styled(Form)`
  flex: 1;
`;

const StyledInput = styled(Input)`
  border-radius: 4px;
  border: 1px solid ${themeTokens.inputBorder};
  padding: 8px 10px;
`;

const AddCompany: React.FC<AddCompanyProps> = ({ open, onClose, selectedCompany, editMode }) => {
  const { useForm } = Form;
  const [form] = useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();
  const { notification } = App.useApp();

  const { data: subscriptionPlansData } = useQuery({
    queryKey: [queryKeys.subscriptionPlansList],
    queryFn: () => companySubscriptionAPI.getAllCompanySubscriptions(),
    retry: false,
    select: res => res.data
  });

  const { mutate, status, reset } = useMutation({
    mutationFn: (data: CompanyDTO) => companyAPI.createCompany(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.companiesList] });
      notification.success({ message: 'Company created successfully' });
    },
    onError: error => {
      notification.success({ message: extractErrorMessage(error, 'Creating company failed') });
    }
  });

  const { mutate: updateCompany } = useMutation({
    mutationFn: (data: { id: number; companyDTO: CompanyDTO }) =>
      companyAPI.updateCompany(data.id, data.companyDTO),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.companyInfo] });
      notification.success({ message: 'Company edited successfully' });
    },
    onError: error => {
      notification.success({ message: extractErrorMessage(error, 'Updating company failed') });
    }
  });

  const initialValues = useMemo(() => {
    if (editMode && selectedCompany) {
      return {
        CompanyName: selectedCompany.name,
        Industry: selectedCompany.industry,
        SubscriptionStatus: selectedCompany.subscriptionStatus
      };
    }
    return undefined;
  }, [editMode, selectedCompany]);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const hasFormChanged = useMemo(() => {
    if (!formValues) return;
    return !isEqual(formValues, initialValues);
  }, [formValues, initialValues]);

  const handleModalClose = () => {
    form.resetFields();
    reset();
    onClose();
  };

  const createCompany = async () => {
    const subscriptionPlans = formValues['SubscriptionPlans']?.map((planId: number) =>
      subscriptionPlansData?.content?.find(plan => plan.id === planId)
    );

    try {
      const data: CompanyDTO = {
        name: formValues['CompanyName'],
        industry: formValues['Industry'],
        subscriptionStatus: formValues['SubscriptionStatus'],
        subscriptionPlans
      };
      if (editMode && selectedCompany) {
        updateCompany({
          id: selectedCompany.id!,
          companyDTO: data
        });
      } else {
        mutate(data);
      }
      handleModalClose();
    } catch {
      notification.error({ message: 'Company creation failed' });
    }
  };

  const handleSave = async () => {
    if (status === APIMutationStatus.success) {
      handleModalClose();
      return;
    }
    try {
      await form.validateFields();
      await createCompany();
      onClose();
    } catch {
      notification.error({ message: 'Validation failed' });
    }
  };

  return (
    <Modal
      title={editMode ? 'Edit company' : 'Add company'}
      centered
      open={open}
      closable={false}
      maskClosable={false}
      onCancel={handleModalClose}
      okText={editMode ? 'Save' : 'Add'}
      onOk={handleSave}
      okButtonProps={{ disabled: !hasFormChanged }}
    >
      <StyledForm layout='vertical' form={form}>
        <Form.Item
          label='Name'
          name='CompanyName'
          rules={[{ required: true, message: 'Please enter Company name!' }]}
        >
          <StyledInput placeholder='Enter company name' />
        </Form.Item>
        <Form.Item label='Industry' name='Industry'>
          <StyledInput placeholder='Enter industry type' />
        </Form.Item>
        <Form.Item
          name='SubscriptionStatus'
          label='Subscription-Status'
          rules={[{ required: true, message: 'Please select subscription status' }]}
        >
          <Select
            placeholder='select the subscription status'
            options={subscriptionStatusOptions}
          />
        </Form.Item>
        {/* TODO: Will be enabled in future
         <Form.Item name='SubscriptionPlans' label='Subscription Plans'>
          <Select
            mode='multiple'
            placeholder='Select subscription plans'
            loading={isPending}
            disabled={isPending}
          >
            {subscriptionPlansData?.data.map(plan => (
              <Option value={plan.id}>{plan.status}</Option>
            ))}
          </Select>
        </Form.Item> */}
      </StyledForm>
    </Modal>
  );
};

export default AddCompany;
