import { useMutation, useQuery } from '@tanstack/react-query';
import { App, Button, Col, Popconfirm, Row } from 'antd';
import { useCallback, useState } from 'react';
import { MdEdit } from 'react-icons/md';
import { RxCrossCircled } from 'react-icons/rx';
import { useNavigate, useParams } from 'react-router-dom';
import { CompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import styled from 'styled-components';
import AddCompany from './addCompany';
import { appRoutes, queryKeys } from '../utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
`;

const Header = styled.div`
  font-weight: 500;
  font-size: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
`;

const Title = styled.p`
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 4px;
`;

const FieldValue = styled.p`
  font-weight: 400;
  font-size: 18px;
  word-wrap: break-word;
`;

const ContentWrapper = styled.div`
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  gap: 25px;
`;

const StyledButton = styled(Button)`
  position: absolute;
  right: 48px;
  bottom: 48px;
`;

const CompanyDetail: React.FC = () => {
  const { companyId } = useParams();
  const navigate = useNavigate();
  const { notification } = App.useApp();
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanyDTO | null>(null);

  const { data } = useQuery({
    queryKey: [queryKeys.companyInfo, companyId],
    queryFn: () => companyAPI.getCompanyById(Number(companyId)),
    enabled: !!companyId,
    select: res => res.data
  });

  const onSuccess = () => {
    notification.success({ message: 'Company deleted Successfully' });
    navigate(`/${appRoutes.company}`);
  };

  const { mutate, isPending: isDeletePending } = useMutation({
    mutationFn: () => companyAPI.deleteCompany(Number(companyId)),
    onSuccess,
    onError: () => notification.error({ message: 'Company deletion failed' })
  });

  const handleEditClick = useCallback((company: CompanyDTO) => {
    setSelectedCompany(company);
    setIsEditModalOpen(true);
  }, []);

  return (
    <Container>
      <Header>
        {data?.name}
        <MdEdit size={24} cursor='pointer' onClick={() => handleEditClick(data!)} />
      </Header>
      <ContentWrapper>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>SubscriptionStatus</Title>
            <FieldValue>{data?.subscriptionStatus || '-'}</FieldValue>
          </Col>
          <Col xs={24} sm={10} offset={2}>
            <Title>Industry</Title>
            <FieldValue>{data?.industry || '-'}</FieldValue>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>SubscriptionPlans</Title>
            {data?.subscriptionPlans
              ? data?.subscriptionPlans?.map(plan => <FieldValue>{plan?.status}</FieldValue>)
              : '-'}
          </Col>
        </Row>
      </ContentWrapper>
      <Popconfirm
        title='Are you sure to delete this company?'
        onConfirm={() => mutate()}
        onCancel={() => null}
        okText='Yes'
        cancelText='No'
      >
        <StyledButton
          danger
          type='primary'
          loading={isDeletePending}
          disabled={isDeletePending}
          icon={<RxCrossCircled />}
          iconPosition='end'
        >
          Delete Company
        </StyledButton>
      </Popconfirm>
      {isEditModalOpen && (
        <AddCompany
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={isEditModalOpen}
          selectedCompany={selectedCompany!}
        />
      )}
    </Container>
  );
};

export default CompanyDetail;
