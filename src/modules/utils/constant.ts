const appRoutes = {
  login: '',
  projects: 'projects',
  dashboard: 'dashboard',
  scopes: 'scopes',
  documents: 'documents',
  bidSheets: 'bidSheets',
  enterprise: 'enterprise',
  user: 'user',
  company: 'company',
  admin: 'admin',
  wyreAIAdmin: 'wyre-ai-admin'
};

enum <PERSON>ey {
  selectedProjectId = 'selectedProjectId',
  isAuthenticated = 'isAuthenticated',
  versionInfo = 'versionInfo',
  instructionsModalChecked = 'instructionsModalChecked'
}

const queryKeys = {
  allProjects: 'allProjects',
  allScopes: 'allScopes',
  allSheets: 'allSheets',
  allSpecifications: 'allSpecifications',
  specsByDivision: 'specsByDivision',
  allBidItems: 'allBidItems',
  companiesList: 'companiesList',
  companyInfo: 'companyInfo',
  rolesList: 'rolesList',
  subscriptionPlansList: 'subscriptionPlansList',
  usersList: 'usersList',
  userInfo: 'userInfo',
  userDetails: 'userDetails',
  projectInfo: 'projectInfo',
  scopeInfo: 'scopeInfo',
  projectDocumentsInfo: 'projectDocumentsInfo',
  projectUsers: 'projectUsers',
  masterScopes: 'masterScopes',
  documentsList: 'documentsList',
  versionList: 'versionList',
  getNotes: 'getNotes',
  uploadFiles: 'uploadFiles',
  changePassword: 'changePassword',
  documentsFilters: 'documentsFilters',
  currentScopeDrawings: 'currentScopeDrawings',
  currentScopeSpecs: 'currentScopeSpecs',
  searchTextData: 'searchTextData',
  currentScopeDrawingsAndSpecs: 'currentScopeDrawingsAndSpecs',
  drawingsAndSpecs: 'drawingsAndSpecs',
  companiesMetrics: 'companiesMetrics',
  projectsMetrics: 'projectsMetrics',
  usersMetrics: 'usersMetrics',
  events: 'events',
  subscriptionSettings: 'subscriptionSettings'
};

enum APIMutationStatus {
  error = 'error',
  idle = 'idle',
  pending = 'pending',
  success = 'success'
}

const disciplines = [
  { label: 'All', value: 'ALL' },
  {
    label: 'Procurement & Contracting Requirements',
    value: 'PROCUREMENT_AND_CONTRACTING_REQUIREMENTS'
  },
  { label: 'General Requirements', value: 'GENERAL_REQUIREMENTS' },
  { label: 'Architectural', value: 'ARCHITECTURAL' },
  { label: 'Structural', value: 'STRUCTURAL' },
  { label: 'Fire Protection', value: 'FIRE_PROTECTION' },
  { label: 'Plumbing', value: 'PLUMBING' },
  { label: 'Mechanical', value: 'MECHANICAL' },
  { label: 'Integrated Automation', value: 'INTEGRATED_AUTOMATION' },
  { label: 'Electrical', value: 'ELECTRICAL' },
  { label: 'Communications', value: 'COMMUNICATIONS' },
  { label: 'Electronic Safety and Security', value: 'ELECTRONIC_SAFETY_AND_SECURITY' },
  { label: 'Site', value: 'SITE' },
  { label: 'Landscape', value: 'LANDSCAPE' },
  { label: 'Transportation', value: 'TRANSPORTATION' },
  { label: 'Waterway, Marine, and Coastal', value: 'WATERWAY_MARINE_AND_COASTAL' },
  { label: 'Process', value: 'PROCESS' },
  { label: 'Material Handling', value: 'MATERIAL_HANDLING' },
  { label: 'Process Heating', value: 'PROCESS_HEATING' },
  {
    label: 'Process Gas and Liquid Handling, Purification, and Storage Equipment',
    value: 'PROCESS_GAS_AND_LIQUID_HANDLING_PURIFICATION_AND_STORAGE_EQUIPMENT'
  },
  {
    label: 'Pollution and Waste Control Equipment',
    value: 'POLLUTION_AND_WASTE_CONTROL_EQUIPMENT'
  },
  {
    label: 'Industry-Specific Manufacturing Equipment',
    value: 'INDUSTRY_SPECIFIC_MANUFACTURING_EQUIPMENT'
  },
  { label: 'Water and Wastewater Equipment', value: 'WATER_AND_WASTEWATER_EQUIPMENT' },
  { label: 'Electrical Power Generation', value: 'ELECTRICAL_POWER_GENERATION' }
];

const jobRoles = [
  'Project Manager',
  'Construction Manager',
  'Site Engineer',
  'Site Supervisor',
  'Document Controller',
  'Quality Control Engineer',
  'MEP Engineer',
  'Planning Engineer'
];

const subscriptionStatus = {
  ACTIVE: 'ACTIVE',
  TRIAL: 'TRIAL',
  SUSPENDED: 'SUSPENDED'
};

const subscriptionStatusOptions = [
  { label: 'Active', value: subscriptionStatus.ACTIVE },
  { label: 'Trial', value: subscriptionStatus.TRIAL },
  { label: 'Suspended', value: subscriptionStatus.SUSPENDED }
];

enum buttonText {
  upload = 'Upload',
  continue = 'Continue'
}

enum analysingDocumentsStatusText {
  uploadingDocuments = 'Uploading Your Documents',
  somethingWentWrong = 'Something went wrong!',
  documentsScoped = 'Documents Analyzed and Scoped!',
  documentsAnalysed = 'Analyzing Your Documents',
  documentsUploadingStatus = 'Confirming Your Documents Upload Status',
  creatingProject = 'Creating Project',
  ScopeBuilderAnalyzingDocuments = 'ScopeBuilder is currently analyzing the documents.',
  ScopeBuilderAnalyzingDocumentsSubText = "Sit back, relax, and we'll send you an email as soon as they're ready to be reviewed."
}

enum changePasswordInputText {
  currentPassword = 'Please enter current password',
  newPassword = 'Please enter new password',
  confirmPassword = 'Please re-enter new password',
  currentPasswordPlaceholder = 'Enter current password',
  newPasswordPlaceholder = 'Enter new password',
  confirmPasswordPlaceholder = 'Re-enter new password'
}

enum fileEnums {
  uploadDrawings = 'Upload Drawings',
  uploadSpecifications = 'Upload Specifications'
}

enum DocProcessingJobStatus {
  noJob = 'NO_JOB',
  queued = 'QUEUED',
  inProgress = 'IN_PROGRESS',
  completed = 'COMPLETED',
  failed = 'FAILED'
}

enum DocumentProcessingIndicatorText {
  title = 'Hang tight! Documents are being processed.',
  bodyText = 'ScopeBuilder is analyzing your documents to build out your scopes.',
  bodySubText = 'You’ll be notified once everything’s ready to review.'
}

export const excludedRoutes = [
  appRoutes.projects,
  appRoutes.user,
  appRoutes.company,
  appRoutes.enterprise
];

export {
  appRoutes,
  StorageKey,
  queryKeys,
  APIMutationStatus,
  disciplines,
  jobRoles,
  subscriptionStatus,
  subscriptionStatusOptions,
  buttonText,
  analysingDocumentsStatusText,
  changePasswordInputText,
  fileEnums,
  DocProcessingJobStatus,
  DocumentProcessingIndicatorText
};
