import * as pdfjs from 'pdfjs-dist';
import pdfWorker from 'pdfjs-dist/build/pdf.worker.mjs?url';
import { BidItemSheetDTO, BidItemSpecificationDTO, DocumentWithPresignedUrlDTO } from 'src/api';

interface SearchResult {
  file: string;
  page: number;
}

interface ThumbnailInfo {
  document: DocumentWithPresignedUrlDTO | null;
  sheet: BidItemSheetDTO;
  thumbnail: string;
}

interface SpecThumbnailInfo {
  document?: DocumentWithPresignedUrlDTO;
  specification: BidItemSpecificationDTO;
  thumbnail: string;
}

pdfjs.GlobalWorkerOptions.workerSrc = pdfWorker;

const generateThumbnails = async (
  doc: DocumentWithPresignedUrlDTO | null,
  sheets: BidItemSheetDTO[]
): Promise<ThumbnailInfo[]> => {
  if (!doc || sheets?.length === 0) return [];
  const thumbnailsInfo: Array<ThumbnailInfo> = [];
  try {
    const loadingTask = pdfjs.getDocument(doc.presignedUrl);
    const pdf = await loadingTask.promise;

    for (const sheet of sheets) {
      if (sheet && sheet.pageNumber) {
        try {
          const page = await pdf.getPage(sheet.pageNumber);
          const viewport = page.getViewport({ scale: 1 });
          const canvas = document.createElement('canvas');
          canvas.width = viewport.width;
          canvas.height = viewport.height;
          const context = canvas.getContext('2d')!;
          const renderContext = {
            canvasContext: context,
            viewport: viewport
          };
          await page.render(renderContext).promise;
          thumbnailsInfo.push({
            document: doc,
            sheet: sheet,
            thumbnail: canvas.toDataURL()
          });
        } catch (pageError) {
          console.error(`Error rendering page ${sheet.pageNumber}:`, pageError);
        }
      }
    }
  } catch (error) {
    console.error('Error loading PDF document:', error);
  }
  return thumbnailsInfo;
};

const findThumbnailsWithText = async (
  searchQuery: string,
  fileUrl?: string,
  pageNumber?: number
): Promise<SearchResult[]> => {
  if (!fileUrl || !pageNumber || !searchQuery) return [];
  const searchResult: Array<SearchResult> = [];

  try {
    const loadingTask = pdfjs.getDocument(fileUrl);
    const pdf = await loadingTask.promise;

    try {
      const page = await pdf.getPage(pageNumber);
      const textContent = await page.getTextContent();
      const pageText = textContent.items.reduce((acc, item: any) => {
        if (item?.str) {
          acc += item.str;
        } else if (item?.text) {
          acc += item.text;
        } else if (item?.chars && Array.isArray(item?.chars)) {
          acc += item.chars.map((char: any) => char.text || char.str || '').join('');
        }
        return acc;
      }, '');
      const textFound = pageText.toLowerCase().includes(searchQuery.toLowerCase());
      if (textFound)
        searchResult.push({
          file: fileUrl,
          page: pageNumber
        });
    } catch (pageError) {
      console.error(`Error rendering page ${pageNumber}:`, pageError);
    }
  } catch (error) {
    console.error('Error loading PDF document:', error);
  }

  return searchResult;
};

const getThumbnailS3Url = (
  companyId?: number,
  projectId?: number,
  documentId?: number,
  page?: number
): string => {
  if (!companyId || !projectId || !documentId || !page) return '';
  return `https://wyreai-local.s3.us-east-1.amazonaws.com/manikanta/thumbnails/${companyId}/${projectId}/${documentId}/original/page_${page}.png`;
};

export default generateThumbnails;
export { findThumbnailsWithText, getThumbnailS3Url };
export type { ThumbnailInfo, SearchResult, SpecThumbnailInfo };
