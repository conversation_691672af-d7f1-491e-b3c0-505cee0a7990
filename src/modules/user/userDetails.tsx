import { useMutation, useQuery } from '@tanstack/react-query';
import { App, Button, Col, Popconfirm, Row } from 'antd';
import { useCallback, useState } from 'react';
import { MdEdit } from 'react-icons/md';
import { RxCrossCircled } from 'react-icons/rx';
import { useNavigate, useParams } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import styled from 'styled-components';
import AddUser from './addUser';
import { appRoutes, queryKeys } from '../utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
`;

const Header = styled.div`
  font-weight: 500;
  font-size: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
`;

const Title = styled.p`
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 4px;
`;

const FieldValue = styled.p`
  font-weight: 400;
  font-size: 18px;
  word-wrap: break-word;
`;

const ContentWrapper = styled.div`
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  gap: 25px;
`;

const StyledButton = styled(Button)`
  position: absolute;
  right: 48px;
  bottom: 48px;
`;

const UserDetail: React.FC = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { notification } = App.useApp();
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserDTO | null>(null);

  const { data } = useQuery({
    queryKey: [queryKeys.userInfo, userId],
    queryFn: () => userAPI.getUserById(Number(userId)),
    enabled: !!userId,
    select: res => res.data
  });

  const onSuccess = () => {
    notification.success({ message: 'User Deleted Successfully' });
    navigate(`/${appRoutes.user}`);
  };

  const { mutate, isPending: isDeletePending } = useMutation({
    mutationFn: () => userAPI.deleteUser(Number(userId)),
    onSuccess,
    onError: () => notification.error({ message: 'User deletion failed' })
  });

  const handleEditClick = useCallback((user: UserDTO) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  }, []);

  return (
    <Container>
      <Header>
        {data?.name}
        <MdEdit size={24} cursor='pointer' onClick={() => handleEditClick(data!)} />
      </Header>
      <ContentWrapper>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>Email</Title>
            <FieldValue>{data?.email || '-'}</FieldValue>
          </Col>
          <Col xs={24} sm={10} offset={2}>
            <Title>Title</Title>
            <FieldValue>{data?.title || '-'}</FieldValue>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={10} offset={2}>
            <Title>Active Status</Title>
            <FieldValue>{data?.isActive}</FieldValue>
          </Col>
        </Row>
      </ContentWrapper>
      <Popconfirm
        title='Are you sure to delete this user?'
        onConfirm={() => mutate()}
        onCancel={() => null}
        okText='Yes'
        cancelText='No'
      >
        <StyledButton
          danger
          type='primary'
          loading={isDeletePending}
          disabled={isDeletePending}
          icon={<RxCrossCircled />}
          iconPosition='end'
        >
          Delete User
        </StyledButton>
      </Popconfirm>
      {isEditModalOpen && (
        <AddUser
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          editMode={true}
          selectedUser={selectedUser!}
        />
      )}
    </Container>
  );
};

export default UserDetail;
