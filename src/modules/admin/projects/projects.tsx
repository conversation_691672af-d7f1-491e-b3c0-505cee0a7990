import {
  ArrowLeftOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  ProjectOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { notification } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  Con<PERSON>er,
  <PERSON>er,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  SearchInput,
  FilterSelect,
  ExportButton,
  StyledTable,
  StatusBadge,
  ProjectName,
  ProjectNumber,
  CompanyName,
  TeamSize,
  BudgetAmount,
  ProgressContainer,
  ProgressBar,
  ProgressText,
  ActionButtons,
  ActionButton,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext,
  FiltersContainer,
  FilterLabel,
  ClearFiltersButton
} from './projects.style';
import { appRoutes } from '../../utils/constant';

interface ProjectData {
  key: string;
  projectName: string;
  projectNumber: string;
  companyName: string;
  status: 'Active' | 'Completed' | 'On Hold' | 'Cancelled';
  startDate: string;
  endDate: string;
  progress: number;
  budget: number;
  teamSize: number;
  manager: string;
}

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const handleExport = () => {
    notification.success({
      message: 'Export Started',
      description: 'Projects data is being exported. You will receive an email when ready.'
    });
  };

  const handleViewProject = (projectId: string) => {
    notification.info({
      message: 'View Project',
      description: `Opening project details for ${projectId}`
    });
  };

  const handleEditProject = (projectId: string) => {
    notification.info({
      message: 'Edit Project',
      description: `Opening edit form for project ${projectId}`
    });
  };

  const clearFilters = () => {
    setSearchText('');
    setStatusFilter('');
  };

  // Mock data for the table
  const mockData: ProjectData[] = [
    {
      key: '1',
      projectName: 'Office Building A',
      projectNumber: 'PRJ-001',
      companyName: 'ABC Construction',
      status: 'Active',
      startDate: '2023-08-01',
      endDate: '2024-06-30',
      progress: 65,
      budget: 2500000,
      teamSize: 8,
      manager: 'Sarah Wilson'
    },
    {
      key: '2',
      projectName: 'Residential Complex B',
      projectNumber: 'PRJ-002',
      companyName: 'XYZ Builders',
      status: 'Active',
      startDate: '2023-10-15',
      endDate: '2024-08-15',
      progress: 45,
      budget: 3200000,
      teamSize: 12,
      manager: 'Tom Anderson'
    },
    {
      key: '3',
      projectName: 'Shopping Mall C',
      projectNumber: 'PRJ-003',
      companyName: 'DEF Developers',
      status: 'Completed',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      progress: 100,
      budget: 5000000,
      teamSize: 15,
      manager: 'Lisa Garcia'
    },
    {
      key: '4',
      projectName: 'Warehouse D',
      projectNumber: 'PRJ-004',
      companyName: 'GHI Logistics',
      status: 'On Hold',
      startDate: '2023-11-01',
      endDate: '2024-09-30',
      progress: 25,
      budget: 1800000,
      teamSize: 6,
      manager: 'Jack Robinson'
    },
    {
      key: '5',
      projectName: 'Hospital E',
      projectNumber: 'PRJ-005',
      companyName: 'JKL Healthcare',
      status: 'Active',
      startDate: '2024-01-15',
      endDate: '2025-01-15',
      progress: 15,
      budget: 8500000,
      teamSize: 20,
      manager: 'Maria Rodriguez'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter data based on search and status
  const filteredData = mockData.filter(project => {
    const matchesSearch =
      searchText === '' ||
      project.projectName.toLowerCase().includes(searchText.toLowerCase()) ||
      project.projectNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      project.companyName.toLowerCase().includes(searchText.toLowerCase());

    const matchesStatus = statusFilter === '' || project.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const columns: ColumnsType<ProjectData> = [
    {
      title: 'Project',
      key: 'project',
      width: 200,
      render: (_, record: ProjectData) => (
        <div>
          <ProjectName>{record.projectName}</ProjectName>
          <ProjectNumber>{record.projectNumber}</ProjectNumber>
        </div>
      )
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 180,
      render: (companyName: string) => <CompanyName>{companyName}</CompanyName>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => <StatusBadge status={status}>{status}</StatusBadge>
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 120,
      render: (date: string) => formatDate(date)
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 120,
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <ProgressContainer>
          <ProgressBar progress={progress} />
          <ProgressText>{progress}%</ProgressText>
        </ProgressContainer>
      )
    },
    {
      title: 'Budget',
      dataIndex: 'budget',
      key: 'budget',
      width: 150,
      render: (budget: number) => <BudgetAmount>{formatCurrency(budget)}</BudgetAmount>
    },
    {
      title: 'Team',
      key: 'team',
      width: 100,
      render: (_, record: ProjectData) => (
        <TeamSize>
          <TeamOutlined />
          {record.teamSize}
        </TeamSize>
      )
    },
    {
      title: 'Manager',
      dataIndex: 'manager',
      key: 'manager',
      width: 150
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: ProjectData) => (
        <ActionButtons>
          <ActionButton
            icon={<EyeOutlined />}
            onClick={() => handleViewProject(record.key)}
            title='View Project'
          />
          <ActionButton
            icon={<EditOutlined />}
            onClick={() => handleEditProject(record.key)}
            title='Edit Project'
          />
        </ActionButtons>
      )
    }
  ];

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects Management</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <SearchInput
            placeholder='Search projects...'
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            allowClear
          />
          <ExportButton type='primary' icon={<DownloadOutlined />} onClick={handleExport}>
            Export
          </ExportButton>
        </HeaderActions>
      </Header>

      <FiltersContainer>
        <FilterLabel>Filters:</FilterLabel>
        <FilterSelect
          placeholder='Status'
          value={statusFilter}
          onChange={setStatusFilter}
          allowClear
        >
          <FilterSelect.Option value='Active'>Active</FilterSelect.Option>
          <FilterSelect.Option value='Completed'>Completed</FilterSelect.Option>
          <FilterSelect.Option value='On Hold'>On Hold</FilterSelect.Option>
          <FilterSelect.Option value='Cancelled'>Cancelled</FilterSelect.Option>
        </FilterSelect>
        {(searchText || statusFilter) && (
          <ClearFiltersButton onClick={clearFilters}>Clear Filters</ClearFiltersButton>
        )}
      </FiltersContainer>

      <StyledTable
        columns={columns}
        dataSource={filteredData}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} projects`
        }}
        rowKey='key'
        bordered
        rowHoverable
        locale={{
          emptyText: (
            <EmptyState>
              <EmptyIcon>
                <ProjectOutlined />
              </EmptyIcon>
              <EmptyText>No projects found</EmptyText>
              <EmptySubtext>Try adjusting your search or filters</EmptySubtext>
            </EmptyState>
          )
        }}
        scroll={{ y: 'calc(100vh - 350px)' }}
      />
    </Container>
  );
};

export default Projects;
