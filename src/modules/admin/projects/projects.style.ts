import { Button, Input, Select, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;

export const SearchInput = styled(Input.Search)`
  width: 300px;
`;

export const FilterSelect = styled(Select)`
  width: 150px;
`;

export const ExportButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StyledTable = styled(Table)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

export const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'active':
        return '#f6ffed';
      case 'completed':
        return '#e6f7ff';
      case 'on hold':
        return '#fff7e6';
      case 'cancelled':
        return '#fff2f0';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'active':
        return '#52c41a';
      case 'completed':
        return '#1890ff';
      case 'on hold':
        return '#faad14';
      case 'cancelled':
        return '#ff4d4f';
      default:
        return '#666';
    }
  }};
  border: 1px solid
    ${props => {
      switch (props.status.toLowerCase()) {
        case 'active':
          return '#b7eb8f';
        case 'completed':
          return '#91d5ff';
        case 'on hold':
          return '#ffe58f';
        case 'cancelled':
          return '#ffccc7';
        default:
          return '#d9d9d9';
      }
    }};
`;

export const ProjectName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const ProjectNumber = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
`;

export const CompanyName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const TeamSize = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
`;

export const BudgetAmount = styled.div`
  font-weight: 600;
  color: ${themeTokens.primaryColor};
`;

export const ProgressContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const ProgressBar = styled.div<{ progress: number }>`
  width: 60px;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: ${props => props.progress}%;
    height: 100%;
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    transition: width 0.3s ease;
  }
`;

export const ProgressText = styled.span`
  font-size: 12px;
  font-weight: 500;
  color: ${themeTokens.textGray};
`;

export const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

export const ActionButton = styled(Button)`
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;

export const FiltersContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  align-items: center;
`;

export const FilterLabel = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const ClearFiltersButton = styled(Button)`
  font-size: 12px;
  height: 28px;
`;
