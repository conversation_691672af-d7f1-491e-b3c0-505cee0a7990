import { Button, Input, Select, Table, Avatar } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;

export const SearchInput = styled(Input.Search)`
  width: 300px;
`;

export const FilterSelect = styled(Select)`
  width: 150px;
`;

export const AddUserButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const ExportButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StyledTable = styled(Table)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

export const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const StyledAvatar = styled(Avatar)`
  background-color: ${themeTokens.primaryColor};
  font-weight: 600;
`;

export const UserDetails = styled.div`
  display: flex;
  flex-direction: column;
`;

export const UserName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const UserEmail = styled.div`
  font-size: 13px;
  color: ${themeTokens.textGray};
`;

export const StatusBadge = styled.span<{ active: boolean }>`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => (props.active ? '#f6ffed' : '#fff2f0')};
  color: ${props => (props.active ? '#52c41a' : '#ff4d4f')};
  border: 1px solid ${props => (props.active ? '#b7eb8f' : '#ffccc7')};
`;

export const RoleBadge = styled.span<{ role: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 4px;
  margin-bottom: 4px;
  display: inline-block;
  background-color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#fff2f0';
      case 'manager':
        return '#f6ffed';
      case 'project manager':
        return '#e6f7ff';
      case 'user':
        return '#f9f0ff';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#ff4d4f';
      case 'manager':
        return '#52c41a';
      case 'project manager':
        return '#1890ff';
      case 'user':
        return '#722ed1';
      default:
        return '#666';
    }
  }};
  border: 1px solid
    ${props => {
      switch (props.role.toLowerCase()) {
        case 'admin':
          return '#ffccc7';
        case 'manager':
          return '#b7eb8f';
        case 'project manager':
          return '#91d5ff';
        case 'user':
          return '#d3adf7';
        default:
          return '#d9d9d9';
      }
    }};
`;

export const RolesContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
`;

export const CompanyName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const LastLoginDate = styled.div`
  font-size: 13px;
  color: ${themeTokens.textGray};
`;

export const ProjectsCount = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: ${themeTokens.primaryColor};
`;

export const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

export const ActionButton = styled(Button)`
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;

export const FiltersContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  align-items: center;
`;

export const FilterLabel = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const ClearFiltersButton = styled(Button)`
  font-size: 12px;
  height: 28px;
`;

export const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

export const StatCard = styled.div`
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
`;

export const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: ${themeTokens.primaryColor};
  margin-bottom: 8px;
`;

export const StatLabel = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;
