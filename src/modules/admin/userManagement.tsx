import { ArrowLeftOutlined, SearchOutlined, FilterOutlined, EyeOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Button, Tag, Tooltip, Input, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

import { queryKeys, appRoutes } from '../utils/constant';
import { formatDate } from '../utils/util';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
`;

const StyledTable = styled(Table<ExtendedUserDTO>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const FilterButtonContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const FilterButton = styled(Button)`
  width: 90px;
`;

const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const EmailTooltipContent = styled.span`
  display: block;
  max-width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

const ViewColumnsButton = styled(Button)`
  margin-left: 16px;
`;

interface ExtendedUserDTO extends UserDTO {
  companyName?: string;
}

const AdminUserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  // Fetch users data
  const { data: usersData, isLoading } = useQuery({
    queryKey: [
      queryKeys.usersList,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      userAPI.getAllUsers(
        currentPage - 1, // page (number)
        pageSize, // size (number)
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined, // sort
        debouncedSearchText || undefined, // search text
        undefined, // email
        columnFilters.isActive || undefined, // isActive
        undefined // companyId - fetch all users
      ),
    select: response => response.data
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    const newFilters: Record<string, any> = {};
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        newFilters[key] = filters[key][0];
      }
    });
    setColumnFilters(newFilters);

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedUserDTO> = useMemo(
    () => [
      {
        title: 'User ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        width: 250,
        sorter: true,
        ...getColumnSearchProps('email', 'email'),
        render: (email: string) => (
          <Tooltip title={email}>
            <EmailTooltipContent>{email || '-'}</EmailTooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <TooltipContent>{companyName || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Active User',
        dataIndex: 'isActive',
        key: 'isActive',
        width: 120,
        sorter: true,
        ...getColumnSelectProps('isActive', [
          { text: 'Active', value: 'true' },
          { text: 'Inactive', value: 'false' }
        ]),
        render: (isActive: boolean) => (
          <Tag color={isActive ? themeTokens.successGreen : themeTokens.dangerRed}>
            {isActive ? 'Yes' : 'No'}
          </Tag>
        )
      },
      {
        title: 'Create Time',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Current Role',
        dataIndex: ['role', 'name'],
        key: 'role',
        width: 150,
        render: (roleName: string) => <Tag color={themeTokens.infoBlue}>{roleName || '-'}</Tag>
      },
      {
        title: 'Last Login',
        dataIndex: 'lastLoginDate',
        key: 'lastLoginDate',
        width: 150,
        render: (lastLoginDate: string) => (lastLoginDate ? formatDate(lastLoginDate) : '-')
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, record) => (
          <Space size='small'>
            <Tooltip title='View Columns'>
              <EyeOutlined style={{ cursor: 'pointer', color: themeTokens.infoBlue }} />
            </Tooltip>
          </Space>
        )
      }
    ],
    [getColumnSearchProps, getColumnSelectProps]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>User Management</PageTitle>
        </HeaderLeft>
      </Header>

      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all users...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
        {/* <ViewColumnsButton icon={<EyeOutlined />} type='default'>
          View Columns
        </ViewColumnsButton> */}
      </GlobalSearchContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={usersData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          rowClassName={() => 'editable-row'}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: usersData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1200 }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default AdminUserManagement;
