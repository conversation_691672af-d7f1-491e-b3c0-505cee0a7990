import { But<PERSON>, Card, Progress } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

export const PreviewButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const GenerateButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const ProjectCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  
  &:hover {
    border-color: ${themeTokens.primaryColor};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

export const ProjectHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

export const ProjectInfo = styled.div`
  flex: 1;
`;

export const ProjectName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const ProjectMeta = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin-bottom: 8px;
`;

export const ProjectStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'active':
        return '#f6ffed';
      case 'completed':
        return '#e6f7ff';
      case 'on hold':
        return '#fff7e6';
      case 'cancelled':
        return '#fff2f0';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'active':
        return '#52c41a';
      case 'completed':
        return '#1890ff';
      case 'on hold':
        return '#faad14';
      case 'cancelled':
        return '#ff4d4f';
      default:
        return '#666';
    }
  }};
  border: 1px solid ${props => {
    switch (props.status.toLowerCase()) {
      case 'active':
        return '#b7eb8f';
      case 'completed':
        return '#91d5ff';
      case 'on hold':
        return '#ffe58f';
      case 'cancelled':
        return '#ffccc7';
      default:
        return '#d9d9d9';
    }
  }};
`;

export const ProjectMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 16px;
`;

export const MetricItem = styled.div`
  text-align: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
`;

export const MetricValue = styled.div`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.primaryColor};
  margin-bottom: 4px;
`;

export const MetricLabel = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
  text-transform: uppercase;
`;

export const ProgressSection = styled.div`
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
`;

export const ProgressLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: ${themeTokens.textBlack};
`;

export const StyledProgress = styled(Progress)`
  .ant-progress-text {
    font-weight: 600;
  }
`;

export const TeamSection = styled.div`
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
`;

export const TeamLabel = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin-bottom: 8px;
`;

export const TeamMembers = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

export const TeamMember = styled.span`
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: ${themeTokens.textBlack};
`;

export const ChartSection = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

export const ChartContainer = styled.div`
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
  color: ${themeTokens.textGray};
`;

export const SummarySection = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

export const SummaryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
`;

export const SummaryCard = styled.div`
  text-align: center;
  padding: 20px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
`;

export const SummaryValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: ${themeTokens.primaryColor};
  margin-bottom: 8px;
`;

export const SummaryLabel = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;
