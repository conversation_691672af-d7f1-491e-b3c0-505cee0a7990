import { ArrowLeftOutlined, DownloadOutlined, ProjectOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import { appRoutes } from '../../../utils/constant';
import {
  <PERSON><PERSON>er,
  <PERSON><PERSON>,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  ExportButton,
  TableContainer,
  StyledTable,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext
} from '../usersRolesReport/usersRolesReport.style';

interface ProjectSummaryData {
  key: string;
  projectName: string;
  projectNumber: string;
  status: 'Active' | 'Completed' | 'On Hold' | 'Cancelled';
  startDate: string;
  endDate: string;
  progress: number;
  budget: number;
  teamSize: number;
}

const ProjectsSummaryReport: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handleExport = () => {
    notification.success({
      message: 'Export Started',
      description: 'Projects Summary report is being exported. You will receive an email when ready.'
    });
  };

  // Mock data for the table
  const mockData: ProjectSummaryData[] = [
    {
      key: '1',
      projectName: 'Office Building A',
      projectNumber: 'PRJ-001',
      status: 'Active',
      startDate: '2023-08-01',
      endDate: '2024-06-30',
      progress: 65,
      budget: 2500000,
      teamSize: 8
    },
    {
      key: '2',
      projectName: 'Residential Complex B',
      projectNumber: 'PRJ-002',
      status: 'Active',
      startDate: '2023-10-15',
      endDate: '2024-08-15',
      progress: 45,
      budget: 3200000,
      teamSize: 12
    },
    {
      key: '3',
      projectName: 'Shopping Mall C',
      projectNumber: 'PRJ-003',
      status: 'Completed',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      progress: 100,
      budget: 5000000,
      teamSize: 15
    },
    {
      key: '4',
      projectName: 'Warehouse D',
      projectNumber: 'PRJ-004',
      status: 'On Hold',
      startDate: '2023-11-01',
      endDate: '2024-09-30',
      progress: 25,
      budget: 1800000,
      teamSize: 6
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const columns: ColumnsType<ProjectSummaryData> = [
    {
      title: 'Project Name',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200,
    },
    {
      title: 'Project Number',
      dataIndex: 'projectNumber',
      key: 'projectNumber',
      width: 150,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <span style={{
          padding: '4px 12px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: 500,
          textTransform: 'uppercase',
          backgroundColor: status === 'Active' ? '#f6ffed' : 
                          status === 'Completed' ? '#e6f7ff' :
                          status === 'On Hold' ? '#fff7e6' : '#fff2f0',
          color: status === 'Active' ? '#52c41a' : 
                 status === 'Completed' ? '#1890ff' :
                 status === 'On Hold' ? '#faad14' : '#ff4d4f',
          border: `1px solid ${status === 'Active' ? '#b7eb8f' : 
                                status === 'Completed' ? '#91d5ff' :
                                status === 'On Hold' ? '#ffe58f' : '#ffccc7'}`
        }}>
          {status}
        </span>
      ),
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 100,
      render: (progress: number) => `${progress}%`,
    },
    {
      title: 'Budget',
      dataIndex: 'budget',
      key: 'budget',
      width: 150,
      render: (budget: number) => formatCurrency(budget),
    },
    {
      title: 'Team Size',
      dataIndex: 'teamSize',
      key: 'teamSize',
      width: 100,
    },
  ];

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects Summary Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <ExportButton 
            type='primary' 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
          >
            Export Report
          </ExportButton>
        </HeaderActions>
      </Header>

      <TableContainer>
        <StyledTable
          columns={columns}
          dataSource={mockData}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} projects`,
          }}
          locale={{
            emptyText: (
              <EmptyState>
                <EmptyIcon>
                  <ProjectOutlined />
                </EmptyIcon>
                <EmptyText>No projects found</EmptyText>
                <EmptySubtext>There are no projects to display in this report</EmptySubtext>
              </EmptyState>
            ),
          }}
        />
      </TableContainer>
    </Container>
  );
};

export default ProjectsSummaryReport;
