import {
  ArrowLeftOutlined,
  EyeOutlined,
  DownloadOutlined,
  ProjectOutlined,
  TeamOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { Form, Select, DatePicker, Spin, notification } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { appRoutes } from '../../../utils/constant';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  PreviewButton,
  GenerateButton,
  ProjectCard,
  ProjectHeader,
  ProjectInfo,
  ProjectName,
  ProjectMeta,
  ProjectStatus,
  StatusBadge,
  ProjectMetrics,
  MetricItem,
  MetricValue,
  MetricLabel,
  ProgressSection,
  ProgressLabel,
  StyledProgress,
  TeamSection,
  TeamLabel,
  TeamMembers,
  TeamMember,
  ChartSection,
  ChartContainer,
  SummarySection,
  SummaryGrid,
  SummaryCard,
  SummaryValue,
  SummaryLabel,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext
} from './projectsSummaryReport.style';
import { ConfigurationSection, StyledForm, FormRow, FilterSection, FilterTitle, PreviewSection, PreviewContent, ReportHeader, ReportTitle, ReportSubtitle, StyledDatePicker, StyledSelect, LoadingContainer } from '../usersRolesReport/usersRolesReport.style';

const { RangePicker } = DatePicker;

interface ProjectData {
  id: string;
  name: string;
  status: 'Active' | 'Completed' | 'On Hold' | 'Cancelled';
  startDate: string;
  endDate: string;
  progress: number;
  budget: number;
  spent: number;
  teamSize: number;
  scopesCount: number;
  teamMembers: string[];
  manager: string;
}

interface ReportConfig {
  scope: 'company' | 'project';
  projectId?: string;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  statusFilter: string[];
  exportFormat: 'pdf' | 'excel' | 'csv';
}

const ProjectsSummaryReport: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    scope: 'company',
    dateRange: [dayjs().subtract(6, 'months'), dayjs()],
    statusFilter: [],
    exportFormat: 'pdf'
  });

  // Mock data for demonstration
  const mockProjectData: ProjectData[] = [
    {
      id: '1',
      name: 'Office Building A',
      status: 'Active',
      startDate: '2023-08-01',
      endDate: '2024-06-30',
      progress: 65,
      budget: 2500000,
      spent: 1625000,
      teamSize: 8,
      scopesCount: 24,
      teamMembers: ['John Doe', 'Jane Smith', 'Mike Johnson'],
      manager: 'Sarah Wilson'
    },
    {
      id: '2',
      name: 'Residential Complex B',
      status: 'Active',
      startDate: '2023-10-15',
      endDate: '2024-08-15',
      progress: 45,
      budget: 3200000,
      spent: 1440000,
      teamSize: 12,
      scopesCount: 36,
      teamMembers: ['Alice Brown', 'Bob Davis', 'Carol White'],
      manager: 'Tom Anderson'
    },
    {
      id: '3',
      name: 'Shopping Mall C',
      status: 'Completed',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      progress: 100,
      budget: 5000000,
      spent: 4850000,
      teamSize: 15,
      scopesCount: 48,
      teamMembers: ['David Lee', 'Emma Wilson', 'Frank Miller'],
      manager: 'Lisa Garcia'
    },
    {
      id: '4',
      name: 'Warehouse D',
      status: 'On Hold',
      startDate: '2023-11-01',
      endDate: '2024-09-30',
      progress: 25,
      budget: 1800000,
      spent: 450000,
      teamSize: 6,
      scopesCount: 18,
      teamMembers: ['Grace Taylor', 'Henry Clark'],
      manager: 'Jack Robinson'
    }
  ];

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handlePreview = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      setReportConfig({ ...reportConfig, ...values });
      setShowPreview(true);
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      notification.success({
        message: 'Report Generated',
        description: 'Your Projects Summary report has been generated and will be sent to your email.'
      });
    } catch (error) {
      notification.error({
        message: 'Generation Failed',
        description: 'Failed to generate report. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = mockProjectData.filter(project => {
    if (reportConfig.statusFilter.length > 0 && !reportConfig.statusFilter.includes(project.status)) {
      return false;
    }
    return true;
  });

  const summaryStats = {
    totalProjects: filteredProjects.length,
    activeProjects: filteredProjects.filter(p => p.status === 'Active').length,
    completedProjects: filteredProjects.filter(p => p.status === 'Completed').length,
    totalBudget: filteredProjects.reduce((sum, p) => sum + p.budget, 0),
    totalSpent: filteredProjects.reduce((sum, p) => sum + p.spent, 0),
    avgProgress: Math.round(filteredProjects.reduce((sum, p) => sum + p.progress, 0) / filteredProjects.length)
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects Summary Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <PreviewButton icon={<EyeOutlined />} onClick={handlePreview} loading={loading}>
            Preview
          </PreviewButton>
          <GenerateButton 
            type='primary' 
            icon={<DownloadOutlined />} 
            onClick={handleGenerate}
            loading={loading}
          >
            Generate Report
          </GenerateButton>
        </HeaderActions>
      </Header>

      <ConfigurationSection title="Report Configuration">
        <StyledForm
          form={form}
          layout="vertical"
          initialValues={{
            scope: 'company',
            dateRange: [dayjs().subtract(6, 'months'), dayjs()],
            statusFilter: [],
            exportFormat: 'pdf'
          }}
        >
          <FormRow>
            <Form.Item
              label="Report Scope"
              name="scope"
              rules={[{ required: true, message: 'Please select report scope' }]}
            >
              <StyledSelect placeholder="Select scope">
                <Select.Option value="company">Company Wide</Select.Option>
                <Select.Option value="project">Specific Project</Select.Option>
              </StyledSelect>
            </Form.Item>

            <Form.Item
              label="Project"
              name="projectId"
              dependencies={['scope']}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue('scope') === 'project',
                  message: 'Please select a project'
                })
              ]}
            >
              <StyledSelect 
                placeholder="Select project"
                disabled={form.getFieldValue('scope') !== 'project'}
              >
                {mockProjectData.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.name}
                  </Select.Option>
                ))}
              </StyledSelect>
            </Form.Item>
          </FormRow>

          <FilterSection>
            <FilterTitle>Filters & Options</FilterTitle>
            <FormRow>
              <Form.Item label="Date Range" name="dateRange">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item label="Status Filter" name="statusFilter">
                <StyledSelect mode="multiple" placeholder="Select statuses to include">
                  <Select.Option value="Active">Active</Select.Option>
                  <Select.Option value="Completed">Completed</Select.Option>
                  <Select.Option value="On Hold">On Hold</Select.Option>
                  <Select.Option value="Cancelled">Cancelled</Select.Option>
                </StyledSelect>
              </Form.Item>
            </FormRow>

            <FormRow>
              <Form.Item label="Export Format" name="exportFormat">
                <StyledSelect placeholder="Select export format">
                  <Select.Option value="pdf">PDF</Select.Option>
                  <Select.Option value="excel">Excel</Select.Option>
                  <Select.Option value="csv">CSV</Select.Option>
                </StyledSelect>
              </Form.Item>
            </FormRow>
          </FilterSection>
        </StyledForm>
      </ConfigurationSection>

      {showPreview && (
        <PreviewSection title="Report Preview">
          {loading ? (
            <LoadingContainer>
              <Spin size="large" />
            </LoadingContainer>
          ) : (
            <PreviewContent>
              <ReportHeader>
                <ReportTitle>Projects Summary Report</ReportTitle>
                <ReportSubtitle>
                  Generated on {new Date().toLocaleDateString()} • 
                  {reportConfig.scope === 'company' ? 'Company Wide' : 'Project Specific'}
                </ReportSubtitle>
              </ReportHeader>

              <SummarySection title="Executive Summary">
                <SummaryGrid>
                  <SummaryCard>
                    <SummaryValue>{summaryStats.totalProjects}</SummaryValue>
                    <SummaryLabel>Total Projects</SummaryLabel>
                  </SummaryCard>
                  <SummaryCard>
                    <SummaryValue>{summaryStats.activeProjects}</SummaryValue>
                    <SummaryLabel>Active Projects</SummaryLabel>
                  </SummaryCard>
                  <SummaryCard>
                    <SummaryValue>{summaryStats.completedProjects}</SummaryValue>
                    <SummaryLabel>Completed Projects</SummaryLabel>
                  </SummaryCard>
                  <SummaryCard>
                    <SummaryValue>{formatCurrency(summaryStats.totalBudget)}</SummaryValue>
                    <SummaryLabel>Total Budget</SummaryLabel>
                  </SummaryCard>
                  <SummaryCard>
                    <SummaryValue>{formatCurrency(summaryStats.totalSpent)}</SummaryValue>
                    <SummaryLabel>Total Spent</SummaryLabel>
                  </SummaryCard>
                  <SummaryCard>
                    <SummaryValue>{summaryStats.avgProgress}%</SummaryValue>
                    <SummaryLabel>Avg. Progress</SummaryLabel>
                  </SummaryCard>
                </SummaryGrid>
              </SummarySection>

              <ChartSection title="Project Status Distribution">
                <ChartContainer>
                  <div>
                    <ProjectOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <div>Chart visualization would be displayed here</div>
                    <div style={{ fontSize: '14px', opacity: 0.7 }}>
                      (Integration with charting library required)
                    </div>
                  </div>
                </ChartContainer>
              </ChartSection>

              {filteredProjects.length > 0 ? (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 600, marginBottom: '16px' }}>
                    Project Details
                  </h3>
                  {filteredProjects.map(project => (
                    <ProjectCard key={project.id}>
                      <ProjectHeader>
                        <ProjectInfo>
                          <ProjectName>{project.name}</ProjectName>
                          <ProjectMeta>
                            <CalendarOutlined /> {formatDate(project.startDate)} - {formatDate(project.endDate)} • 
                            <TeamOutlined /> {project.manager}
                          </ProjectMeta>
                          <ProjectStatus>
                            <StatusBadge status={project.status}>{project.status}</StatusBadge>
                          </ProjectStatus>
                        </ProjectInfo>
                      </ProjectHeader>

                      <ProjectMetrics>
                        <MetricItem>
                          <MetricValue>{formatCurrency(project.budget)}</MetricValue>
                          <MetricLabel>Budget</MetricLabel>
                        </MetricItem>
                        <MetricItem>
                          <MetricValue>{formatCurrency(project.spent)}</MetricValue>
                          <MetricLabel>Spent</MetricLabel>
                        </MetricItem>
                        <MetricItem>
                          <MetricValue>{project.teamSize}</MetricValue>
                          <MetricLabel>Team Size</MetricLabel>
                        </MetricItem>
                        <MetricItem>
                          <MetricValue>{project.scopesCount}</MetricValue>
                          <MetricLabel>Scopes</MetricLabel>
                        </MetricItem>
                      </ProjectMetrics>

                      <ProgressSection>
                        <ProgressLabel>
                          <span>Project Progress</span>
                          <span>{project.progress}%</span>
                        </ProgressLabel>
                        <StyledProgress 
                          percent={project.progress} 
                          showInfo={false}
                          strokeColor={{
                            '0%': '#108ee9',
                            '100%': '#87d068',
                          }}
                        />
                      </ProgressSection>

                      <TeamSection>
                        <TeamLabel>Team Members</TeamLabel>
                        <TeamMembers>
                          {project.teamMembers.map((member, index) => (
                            <TeamMember key={index}>{member}</TeamMember>
                          ))}
                        </TeamMembers>
                      </TeamSection>
                    </ProjectCard>
                  ))}
                </div>
              ) : (
                <EmptyState>
                  <EmptyIcon>
                    <ProjectOutlined />
                  </EmptyIcon>
                  <EmptyText>No projects found</EmptyText>
                  <EmptySubtext>Try adjusting your filters to see more results</EmptySubtext>
                </EmptyState>
              )}
            </PreviewContent>
          )}
        </PreviewSection>
      )}
    </Container>
  );
};

export default ProjectsSummaryReport;
