import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const PageDescription = styled.p`
  font-size: 16px;
  color: ${themeTokens.textGray};
  margin-bottom: 32px;
  max-width: 800px;
`;

export const ReportsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

export const ReportCard = styled(Card)`
  height: 160px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${themeTokens.inputBorder};
  border-radius: 12px;
  overflow: hidden;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    border-color: ${themeTokens.primaryColor};
  }

  .ant-card-body {
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
`;

export const ReportImageWrapper = styled.div`
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ReportImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
`;

export const ReportTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 12px;
  line-height: 1.3;
`;

export const ReportDescription = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  line-height: 1.5;
  margin-bottom: 16px;
  flex: 1;
`;

export const ReportFeatures = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
`;

export const ReportFeature = styled.li`
  font-size: 13px;
  color: ${themeTokens.textGray};
  margin-bottom: 6px;
  position: relative;
  padding-left: 16px;

  &:before {
    content: '•';
    color: ${themeTokens.primaryColor};
    font-weight: bold;
    position: absolute;
    left: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

export const ReportAction = styled.div`
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
`;

export const GenerateButton = styled(Button)`
  width: 100%;
  height: 40px;
  font-weight: 500;
  border-radius: 6px;
`;

export const ComingSoonBadge = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
