import { ArrowLeftOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

// Import report images (using existing assets as placeholders)
import usersReportImage from '../../../assets/images/wyreAIIcon.png';
import projectsReportImage from '../../../assets/images/project.svg';
import scopesReportImage from '../../../assets/images/scope.svg';

import { appRoutes } from '../../utils/constant';
import {
  Container,
  Header,
  BackButton,
  PageTitle,
  PageDescription,
  ReportsGrid,
  ReportCard,
  ReportImageWrapper,
  ReportImage,
  ReportTitle,
  ReportDescription,
  ReportFeatures,
  ReportFeature,
  ReportAction,
  GenerateButton,
  ComingSoonBadge
} from './reports.style';

interface ReportType {
  id: string;
  title: string;
  description: string;
  features: string[];
  image: string;
  route?: string;
  comingSoon?: boolean;
}

const Reports: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const reportTypes: ReportType[] = [
    {
      id: 'users-roles',
      title: 'Users & Roles Report',
      description:
        'Comprehensive overview of all users in your company, their assigned roles, permissions, and activity summaries.',
      features: [
        'User activity and login statistics',
        'Role assignments and permissions',
        'Company-wide or per-project filtering',
        'Export to PDF, Excel, or CSV formats'
      ],
      image: usersReportImage,
      comingSoon: true
    },
    {
      id: 'projects-summary',
      title: 'Projects Summary Report',
      description:
        'Consolidated summary of all projects under your company with status tracking, key metrics, and progress indicators.',
      features: [
        'Project status and timeline tracking',
        'Budget and resource allocation',
        'Team assignments and workload',
        'Customizable date ranges and filters'
      ],
      image: projectsReportImage,
      comingSoon: true
    },
    {
      id: 'scopes-bid-items',
      title: 'Scopes & Bid Items Report',
      description:
        'Detailed analysis of project scopes and bid items with cost breakdowns, specifications, and completion status.',
      features: [
        'Detailed scope specifications',
        'Bid item cost analysis',
        'Progress tracking per scope',
        'Per-project detailed reporting'
      ],
      image: scopesReportImage,
      comingSoon: true
    }
  ];

  const handleReportClick = (report: ReportType) => {
    if (report.comingSoon) {
      notification.info({
        message: 'Coming Soon',
        description: 'This report type is currently under development and will be available soon.'
      });
      return;
    }

    if (report.route) {
      navigate(report.route);
    }
  };

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Reports</PageTitle>
      </Header>

      <PageDescription>
        Generate, view, and export detailed project data summaries. Reports support customizable
        filters and formats to provide stakeholders with actionable insights and support
        decision-making across the project lifecycle.
      </PageDescription>

      <ReportsGrid>
        {reportTypes.map(report => (
          <ReportCard
            key={report.id}
            onClick={() => handleReportClick(report)}
            style={{
              cursor: report.comingSoon ? 'not-allowed' : 'pointer',
              opacity: report.comingSoon ? 0.7 : 1
            }}
          >
            {report.comingSoon && <ComingSoonBadge>Coming Soon</ComingSoonBadge>}

            <ReportImageWrapper>
              <ReportImage src={report.image} alt={report.title} />
            </ReportImageWrapper>

            <ReportTitle>{report.title}</ReportTitle>

            <ReportDescription>{report.description}</ReportDescription>

            <ReportFeatures>
              {report.features.map((feature, index) => (
                <ReportFeature key={index}>{feature}</ReportFeature>
              ))}
            </ReportFeatures>

            <ReportAction>
              <GenerateButton type='primary' disabled={report.comingSoon}>
                Generate Report
              </GenerateButton>
            </ReportAction>
          </ReportCard>
        ))}
      </ReportsGrid>
    </Container>
  );
};

export default Reports;
