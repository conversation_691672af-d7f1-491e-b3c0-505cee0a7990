import { ArrowLeftOutlined } from '@ant-design/icons';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  BackButton,
  PageTitle,
  ReportsGrid,
  ReportCard,
  ReportTitle,
  ReportDescription
} from './reports.style';
import projectsReportImage from '../../../assets/images/project.svg';
import scopesReportImage from '../../../assets/images/scope.svg';
import usersReportImage from '../../../assets/images/wyreAIIcon.png';

import { appRoutes } from '../../utils/constant';

interface ReportType {
  id: string;
  title: string;
  description: string;
  image: string;
  route?: string;
  comingSoon?: boolean;
}

const Reports: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const reportTypes: ReportType[] = [
    {
      id: 'users-roles',
      title: 'Users & Roles Report',
      description:
        'Overview of all users in the company, their roles, and activity summaries. This report can be generated company wide or per project.',
      image: usersReportImage,
      route: `/${appRoutes.admin}/reports/users-roles`
    },
    {
      id: 'projects-summary',
      title: 'Projects Summary',
      description:
        'Consolidated summary of all projects under the company, including status and key metrics. This report can be generated company wide or per project.',
      image: projectsReportImage
    },
    {
      id: 'scopes-bid-items',
      title: 'Scopes & Bid Items Summary (per project)',
      description:
        'Detailed report showing scopes and bid items. This report will be generated for one project at a time.',
      image: scopesReportImage
    }
  ];

  const handleReportClick = (report: ReportType) => {
    if (report.route) {
      navigate(report.route);
    }
  };

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Reports</PageTitle>
      </Header>
      <ReportsGrid>
        {reportTypes.map(report => (
          <ReportCard
            key={report.id}
            onClick={() => handleReportClick(report)}
            style={{ cursor: 'pointer' }}
          >
            <ReportTitle>{report.title}</ReportTitle>
            <ReportDescription>{report.description}</ReportDescription>
          </ReportCard>
        ))}
      </ReportsGrid>
    </Container>
  );
};

export default Reports;
