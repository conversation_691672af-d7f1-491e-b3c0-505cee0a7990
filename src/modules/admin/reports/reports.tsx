import { ArrowLeftOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

// Import report images (using existing assets as placeholders)
import usersReportImage from '../../../assets/images/wyreAIIcon.png';
import projectsReportImage from '../../../assets/images/project.svg';
import scopesReportImage from '../../../assets/images/scope.svg';

import { appRoutes } from '../../utils/constant';
import {
  Container,
  Header,
  BackButton,
  PageTitle,
  PageDescription,
  ReportsGrid,
  ReportCard,
  ReportImageWrapper,
  ReportImage,
  ReportTitle,
  ReportDescription,
  ReportFeatures,
  ReportFeature,
  ReportAction,
  GenerateButton,
  ComingSoonBadge
} from './reports.style';

interface ReportType {
  id: string;
  title: string;
  description: string;
  features: string[];
  image: string;
  route?: string;
  comingSoon?: boolean;
}

const Reports: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const reportTypes: ReportType[] = [
    {
      id: 'users-roles',
      title: 'Users & Roles Report',
      description:
        'Overview of all users in the company, their roles, and activity summaries. This report can be generated company wide or per project.',
      features: [
        'User activity and login statistics',
        'Role assignments and permissions',
        'Company-wide or per-project filtering',
        'Export to PDF, Excel, or CSV formats'
      ],
      image: usersReportImage,
      route: `/${appRoutes.admin}/reports/users-roles`
    },
    {
      id: 'projects-summary',
      title: 'Projects Summary',
      description:
        'Consolidated summary of all projects under the company, including status and key metrics. This report can be generated company wide or per project.',
      features: [
        'Project status and timeline tracking',
        'Budget and resource allocation',
        'Team assignments and workload',
        'Customizable date ranges and filters'
      ],
      image: projectsReportImage,
      route: `/${appRoutes.admin}/reports/projects-summary`
    },
    {
      id: 'scopes-bid-items',
      title: 'Scopes & Bid Items Summary (per project)',
      description:
        'Detailed report showing scopes and bid items. This report will be generated for one project at a time.',
      features: [
        'Detailed scope specifications',
        'Bid item cost analysis',
        'Progress tracking per scope',
        'Per-project detailed reporting'
      ],
      image: scopesReportImage,
      route: `/${appRoutes.admin}/reports/scopes-bid-items`
    }
  ];

  const handleReportClick = (report: ReportType) => {
    if (report.route) {
      navigate(report.route);
    }
  };

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Reports</PageTitle>
      </Header>

      <PageDescription>
        Generate, view, and export detailed project data summaries. Reports support customizable
        filters and formats to provide stakeholders with actionable insights and support
        decision-making across the project lifecycle.
      </PageDescription>

      <ReportsGrid>
        {reportTypes.map(report => (
          <ReportCard
            key={report.id}
            onClick={() => handleReportClick(report)}
            style={{ cursor: 'pointer' }}
          >
            <ReportImageWrapper>
              <ReportImage src={report.image} alt={report.title} />
            </ReportImageWrapper>

            <ReportTitle>{report.title}</ReportTitle>

            <ReportDescription>{report.description}</ReportDescription>

            <ReportFeatures>
              {report.features.map((feature, index) => (
                <ReportFeature key={index}>{feature}</ReportFeature>
              ))}
            </ReportFeatures>

            <ReportAction>
              <GenerateButton type='primary'>View Report</GenerateButton>
            </ReportAction>
          </ReportCard>
        ))}
      </ReportsGrid>
    </Container>
  );
};

export default Reports;
