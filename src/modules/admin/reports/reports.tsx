import {
  ArrowLeftOutlined,
  UserOutlined,
  ProjectOutlined,
  FileTextOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { notification } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import { appRoutes } from '../../utils/constant';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  BackButton,
  PageTitle,
  PageDescription,
  ReportsGrid,
  ReportCard,
  UsersReportIcon,
  ProjectsReportIcon,
  ScopesReportIcon,
  ReportTitle,
  ReportDescription,
  ReportFeatures,
  ReportFeature,
  ReportAction,
  GenerateButton,
  ComingSoonBadge,
  QuickStatsSection,
  QuickStatsTitle,
  StatsGrid,
  StatItem,
  StatValue,
  StatLabel,
  RecentReportsSection,
  SectionTitle,
  RecentReportsList,
  RecentReportItem,
  RecentReportInfo,
  RecentReportName,
  RecentReportMeta,
  DownloadButton
} from './reports.style';

interface ReportType {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon: React.ReactNode;
  iconWrapper: React.ComponentType<any>;
  route?: string;
  comingSoon?: boolean;
}

interface RecentReport {
  id: string;
  name: string;
  type: string;
  generatedDate: string;
  generatedBy: string;
  downloadUrl?: string;
}

const Reports: React.FC = () => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}`);
  };

  const reportTypes: ReportType[] = [
    {
      id: 'users-roles',
      title: 'Users & Roles Report',
      description: 'Comprehensive overview of all users in your company, their assigned roles, permissions, and activity summaries.',
      features: [
        'User activity and login statistics',
        'Role assignments and permissions',
        'Company-wide or per-project filtering',
        'Export to PDF, Excel, or CSV formats'
      ],
      icon: <UserOutlined />,
      iconWrapper: UsersReportIcon,
      route: `/${appRoutes.admin}/reports/users-roles`
    },
    {
      id: 'projects-summary',
      title: 'Projects Summary Report',
      description: 'Consolidated summary of all projects under your company with status tracking, key metrics, and progress indicators.',
      features: [
        'Project status and timeline tracking',
        'Budget and resource allocation',
        'Team assignments and workload',
        'Customizable date ranges and filters'
      ],
      icon: <ProjectOutlined />,
      iconWrapper: ProjectsReportIcon,
      route: `/${appRoutes.admin}/reports/projects-summary`
    },
    {
      id: 'scopes-bid-items',
      title: 'Scopes & Bid Items Report',
      description: 'Detailed analysis of project scopes and bid items with cost breakdowns, specifications, and completion status.',
      features: [
        'Detailed scope specifications',
        'Bid item cost analysis',
        'Progress tracking per scope',
        'Per-project detailed reporting'
      ],
      icon: <FileTextOutlined />,
      iconWrapper: ScopesReportIcon,
      route: `/${appRoutes.admin}/reports/scopes-bid-items`
    }
  ];

  // Mock data for quick stats
  const quickStats = [
    { label: 'Total Projects', value: '24' },
    { label: 'Active Users', value: '156' },
    { label: 'Reports Generated', value: '89' },
    { label: 'This Month', value: '12' }
  ];

  // Mock data for recent reports
  const recentReports: RecentReport[] = [
    {
      id: '1',
      name: 'Monthly Users & Roles Report',
      type: 'Users & Roles',
      generatedDate: '2024-01-15',
      generatedBy: 'John Admin',
      downloadUrl: '#'
    },
    {
      id: '2',
      name: 'Q4 Projects Summary',
      type: 'Projects Summary',
      generatedDate: '2024-01-10',
      generatedBy: 'Jane Manager',
      downloadUrl: '#'
    },
    {
      id: '3',
      name: 'Office Building A - Scopes Report',
      type: 'Scopes & Bid Items',
      generatedDate: '2024-01-08',
      generatedBy: 'Mike PM',
      downloadUrl: '#'
    }
  ];

  const handleReportClick = (report: ReportType) => {
    if (report.comingSoon) {
      notification.info({
        message: 'Coming Soon',
        description: 'This report type is currently under development and will be available soon.'
      });
      return;
    }

    if (report.route) {
      navigate(report.route);
    }
  };

  const handleDownloadReport = (report: RecentReport) => {
    notification.success({
      message: 'Download Started',
      description: `Downloading ${report.name}...`
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Reports</PageTitle>
      </Header>

      <PageDescription>
        Generate, view, and export detailed project data summaries. Reports support customizable 
        filters and formats to provide stakeholders with actionable insights and support 
        decision-making across the project lifecycle.
      </PageDescription>

      <QuickStatsSection>
        <QuickStatsTitle>Quick Stats</QuickStatsTitle>
        <StatsGrid>
          {quickStats.map((stat, index) => (
            <StatItem key={index}>
              <StatValue>{stat.value}</StatValue>
              <StatLabel>{stat.label}</StatLabel>
            </StatItem>
          ))}
        </StatsGrid>
      </QuickStatsSection>

      <ReportsGrid>
        {reportTypes.map((report) => {
          const IconWrapper = report.iconWrapper;
          return (
            <ReportCard
              key={report.id}
              onClick={() => handleReportClick(report)}
              style={{
                cursor: report.comingSoon ? 'not-allowed' : 'pointer',
                opacity: report.comingSoon ? 0.7 : 1
              }}
            >
              {report.comingSoon && <ComingSoonBadge>Coming Soon</ComingSoonBadge>}
              
              <IconWrapper>
                {report.icon}
              </IconWrapper>
              
              <ReportTitle>{report.title}</ReportTitle>
              
              <ReportDescription>{report.description}</ReportDescription>
              
              <ReportFeatures>
                {report.features.map((feature, index) => (
                  <ReportFeature key={index}>{feature}</ReportFeature>
                ))}
              </ReportFeatures>
              
              <ReportAction>
                <GenerateButton 
                  type='primary' 
                  disabled={report.comingSoon}
                >
                  Generate Report
                </GenerateButton>
              </ReportAction>
            </ReportCard>
          );
        })}
      </ReportsGrid>

      <RecentReportsSection>
        <SectionTitle>Recent Reports</SectionTitle>
        <RecentReportsList>
          {recentReports.map((report) => (
            <RecentReportItem key={report.id}>
              <RecentReportInfo>
                <RecentReportName>{report.name}</RecentReportName>
                <RecentReportMeta>
                  {report.type} • Generated on {formatDate(report.generatedDate)} by {report.generatedBy}
                </RecentReportMeta>
              </RecentReportInfo>
              <DownloadButton
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadReport(report)}
              >
                Download
              </DownloadButton>
            </RecentReportItem>
          ))}
        </RecentReportsList>
      </RecentReportsSection>
    </Container>
  );
};

export default Reports;
