import { But<PERSON>, Card, Collapse } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

export const PreviewButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const GenerateButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const ScopeCard = styled(Card)`
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  
  .ant-card-head {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
  }
`;

export const ScopeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const ScopeInfo = styled.div`
  flex: 1;
`;

export const ScopeName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const ScopeMeta = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin-bottom: 8px;
`;

export const ScopeMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

export const MetricItem = styled.div`
  text-align: center;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
`;

export const MetricValue = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.primaryColor};
  margin-bottom: 4px;
`;

export const MetricLabel = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
  text-transform: uppercase;
`;

export const BidItemsSection = styled.div`
  margin-top: 20px;
`;

export const BidItemsTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 12px;
`;

export const StyledCollapse = styled(Collapse)`
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  
  .ant-collapse-item {
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .ant-collapse-header {
    padding: 12px 16px !important;
    background: #fafafa;
  }
  
  .ant-collapse-content-box {
    padding: 16px !important;
  }
`;

export const BidItemHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

export const BidItemInfo = styled.div`
  flex: 1;
`;

export const BidItemName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const BidItemMeta = styled.div`
  font-size: 13px;
  color: ${themeTokens.textGray};
`;

export const BidItemCost = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.primaryColor};
`;

export const BidItemDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

export const DetailItem = styled.div`
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
`;

export const DetailLabel = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
  text-transform: uppercase;
  margin-bottom: 4px;
`;

export const DetailValue = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const SpecificationSection = styled.div`
  margin-top: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
`;

export const SpecTitle = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 8px;
`;

export const SpecContent = styled.div`
  font-size: 14px;
  color: ${themeTokens.textBlack};
  line-height: 1.5;
`;

export const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'completed':
        return '#f6ffed';
      case 'in progress':
        return '#e6f7ff';
      case 'pending':
        return '#fff7e6';
      case 'not started':
        return '#fafafa';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.status.toLowerCase()) {
      case 'completed':
        return '#52c41a';
      case 'in progress':
        return '#1890ff';
      case 'pending':
        return '#faad14';
      case 'not started':
        return '#666';
      default:
        return '#666';
    }
  }};
  border: 1px solid ${props => {
    switch (props.status.toLowerCase()) {
      case 'completed':
        return '#b7eb8f';
      case 'in progress':
        return '#91d5ff';
      case 'pending':
        return '#ffe58f';
      case 'not started':
        return '#d9d9d9';
      default:
        return '#d9d9d9';
    }
  }};
`;

export const CostSummarySection = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

export const CostGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
`;

export const CostCard = styled.div`
  text-align: center;
  padding: 20px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
`;

export const CostValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: ${themeTokens.primaryColor};
  margin-bottom: 8px;
`;

export const CostLabel = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;
