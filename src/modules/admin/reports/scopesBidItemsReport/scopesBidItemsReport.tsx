import { ArrowLeftOutlined, DownloadOutlined, FileTextOutlined } from '@ant-design/icons';
import { notification, Select } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { appRoutes } from '../../../utils/constant';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  ExportButton,
  TableContainer,
  StyledTable,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext
} from '../usersRolesReport/usersRolesReport.style';

interface ScopeBidItemData {
  key: string;
  scopeName: string;
  division: string;
  bidItemName: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  status: 'Completed' | 'In Progress' | 'Pending' | 'Not Started';
}

const ScopesBidItemsReport: React.FC = () => {
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<string>('PRJ-001');

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handleExport = () => {
    notification.success({
      message: 'Export Started',
      description: 'Scopes & Bid Items report is being exported. You will receive an email when ready.'
    });
  };

  // Mock projects data
  const projects = [
    { value: 'PRJ-001', label: 'Office Building A' },
    { value: 'PRJ-002', label: 'Residential Complex B' },
    { value: 'PRJ-003', label: 'Shopping Mall C' },
    { value: 'PRJ-004', label: 'Warehouse D' }
  ];

  // Mock data for the table
  const mockData: ScopeBidItemData[] = [
    {
      key: '1',
      scopeName: 'Electrical Systems',
      division: 'Division 26',
      bidItemName: 'Main Electrical Panel',
      quantity: 1,
      unit: 'EA',
      unitCost: 15000,
      totalCost: 15000,
      status: 'Completed'
    },
    {
      key: '2',
      scopeName: 'Electrical Systems',
      division: 'Division 26',
      bidItemName: 'LED Light Fixtures',
      quantity: 150,
      unit: 'EA',
      unitCost: 120,
      totalCost: 18000,
      status: 'In Progress'
    },
    {
      key: '3',
      scopeName: 'HVAC Systems',
      division: 'Division 23',
      bidItemName: 'Rooftop HVAC Unit',
      quantity: 2,
      unit: 'EA',
      unitCost: 45000,
      totalCost: 90000,
      status: 'Not Started'
    },
    {
      key: '4',
      scopeName: 'Plumbing Systems',
      division: 'Division 22',
      bidItemName: 'Water Heater',
      quantity: 2,
      unit: 'EA',
      unitCost: 8500,
      totalCost: 17000,
      status: 'Completed'
    },
    {
      key: '5',
      scopeName: 'Plumbing Systems',
      division: 'Division 22',
      bidItemName: 'Pipe Fittings',
      quantity: 500,
      unit: 'LF',
      unitCost: 25,
      totalCost: 12500,
      status: 'In Progress'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const columns: ColumnsType<ScopeBidItemData> = [
    {
      title: 'Scope Name',
      dataIndex: 'scopeName',
      key: 'scopeName',
      width: 180,
    },
    {
      title: 'Division',
      dataIndex: 'division',
      key: 'division',
      width: 120,
    },
    {
      title: 'Bid Item Name',
      dataIndex: 'bidItemName',
      key: 'bidItemName',
      width: 200,
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number, record: ScopeBidItemData) => 
        `${quantity} ${record.unit}`,
    },
    {
      title: 'Unit Cost',
      dataIndex: 'unitCost',
      key: 'unitCost',
      width: 120,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: 'Total Cost',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <span style={{
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontWeight: 500,
          textTransform: 'uppercase',
          backgroundColor: status === 'Completed' ? '#f6ffed' : 
                          status === 'In Progress' ? '#e6f7ff' :
                          status === 'Pending' ? '#fff7e6' : '#fafafa',
          color: status === 'Completed' ? '#52c41a' : 
                 status === 'In Progress' ? '#1890ff' :
                 status === 'Pending' ? '#faad14' : '#666',
          border: `1px solid ${status === 'Completed' ? '#b7eb8f' : 
                                status === 'In Progress' ? '#91d5ff' :
                                status === 'Pending' ? '#ffe58f' : '#d9d9d9'}`
        }}>
          {status}
        </span>
      ),
    },
  ];

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Scopes & Bid Items Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <Select
            value={selectedProject}
            onChange={setSelectedProject}
            options={projects}
            style={{ width: 200, marginRight: 12 }}
            placeholder="Select Project"
          />
          <ExportButton 
            type='primary' 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
          >
            Export Report
          </ExportButton>
        </HeaderActions>
      </Header>

      <TableContainer>
        <StyledTable
          columns={columns}
          dataSource={mockData}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          locale={{
            emptyText: (
              <EmptyState>
                <EmptyIcon>
                  <FileTextOutlined />
                </EmptyIcon>
                <EmptyText>No scopes or bid items found</EmptyText>
                <EmptySubtext>Select a project to view scopes and bid items</EmptySubtext>
              </EmptyState>
            ),
          }}
        />
      </TableContainer>
    </Container>
  );
};

export default ScopesBidItemsReport;
