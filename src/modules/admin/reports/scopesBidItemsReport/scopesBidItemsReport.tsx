import {
  ArrowLeftOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { Form, Select, notification, Spin } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { appRoutes } from '../../../utils/constant';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  PreviewButton,
  GenerateButton,
  ScopeCard,
  ScopeHeader,
  ScopeInfo,
  ScopeName,
  ScopeMeta,
  ScopeMetrics,
  MetricItem,
  MetricValue,
  MetricLabel,
  BidItemsSection,
  BidItemsTitle,
  StyledCollapse,
  BidItemHeader,
  BidItemInfo,
  BidItemName,
  BidItemMeta,
  BidItemCost,
  BidItemDetails,
  DetailItem,
  DetailLabel,
  DetailValue,
  SpecificationSection,
  SpecTitle,
  SpecContent,
  StatusBadge,
  CostSummarySection,
  CostGrid,
  CostCard,
  CostValue,
  CostLabel,
  EmptyState,
  EmptyIcon,
  EmptyText,
  EmptySubtext
} from './scopesBidItemsReport.style';
import { ConfigurationSection, StyledForm, FormRow, PreviewSection, PreviewContent, ReportHeader, ReportTitle, ReportSubtitle, StyledSelect, LoadingContainer } from '../usersRolesReport/usersRolesReport.style';

interface BidItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  status: 'Completed' | 'In Progress' | 'Pending' | 'Not Started';
  specifications: string;
}

interface ScopeData {
  id: string;
  name: string;
  division: string;
  description: string;
  status: 'Completed' | 'In Progress' | 'Pending' | 'Not Started';
  progress: number;
  totalCost: number;
  bidItemsCount: number;
  bidItems: BidItem[];
}

interface ReportConfig {
  projectId: string;
  scopeFilter: string[];
  statusFilter: string[];
  exportFormat: 'pdf' | 'excel' | 'csv';
}

const ScopesBidItemsReport: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    projectId: '',
    scopeFilter: [],
    statusFilter: [],
    exportFormat: 'pdf'
  });

  // Mock data for demonstration
  const mockProjects = [
    { id: '1', name: 'Office Building A' },
    { id: '2', name: 'Residential Complex B' },
    { id: '3', name: 'Shopping Mall C' }
  ];

  const mockScopeData: ScopeData[] = [
    {
      id: '1',
      name: 'Electrical Systems',
      division: 'Division 26',
      description: 'Complete electrical installation including power distribution, lighting, and controls',
      status: 'In Progress',
      progress: 75,
      totalCost: 450000,
      bidItemsCount: 12,
      bidItems: [
        {
          id: '1',
          name: 'Main Electrical Panel',
          description: '400A main electrical distribution panel',
          quantity: 1,
          unit: 'EA',
          unitCost: 15000,
          totalCost: 15000,
          status: 'Completed',
          specifications: 'Square D QO series, 400A main breaker, 42 circuit capacity'
        },
        {
          id: '2',
          name: 'LED Light Fixtures',
          description: 'Commercial grade LED ceiling fixtures',
          quantity: 150,
          unit: 'EA',
          unitCost: 120,
          totalCost: 18000,
          status: 'In Progress',
          specifications: 'Lithonia 2x4 LED troffer, 4000K, 3500 lumens, dimmable'
        }
      ]
    },
    {
      id: '2',
      name: 'HVAC Systems',
      division: 'Division 23',
      description: 'Heating, ventilation, and air conditioning systems',
      status: 'Pending',
      progress: 25,
      totalCost: 680000,
      bidItemsCount: 18,
      bidItems: [
        {
          id: '3',
          name: 'Rooftop HVAC Unit',
          description: '50-ton rooftop package unit',
          quantity: 2,
          unit: 'EA',
          unitCost: 45000,
          totalCost: 90000,
          status: 'Not Started',
          specifications: 'Carrier 50TCQ series, 460V/3Ph/60Hz, R-410A refrigerant'
        }
      ]
    },
    {
      id: '3',
      name: 'Plumbing Systems',
      division: 'Division 22',
      description: 'Complete plumbing installation including fixtures and piping',
      status: 'Completed',
      progress: 100,
      totalCost: 320000,
      bidItemsCount: 24,
      bidItems: [
        {
          id: '4',
          name: 'Water Heater',
          description: 'Commercial gas water heater',
          quantity: 2,
          unit: 'EA',
          unitCost: 8500,
          totalCost: 17000,
          status: 'Completed',
          specifications: 'Bradford White 100-gallon, natural gas, 199,000 BTU'
        }
      ]
    }
  ];

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handlePreview = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      setReportConfig({ ...reportConfig, ...values });
      setShowPreview(true);
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      notification.success({
        message: 'Report Generated',
        description: 'Your Scopes & Bid Items report has been generated and will be sent to your email.'
      });
    } catch (error) {
      notification.error({
        message: 'Generation Failed',
        description: 'Failed to generate report. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredScopes = mockScopeData.filter(scope => {
    if (reportConfig.scopeFilter.length > 0 && !reportConfig.scopeFilter.includes(scope.id)) {
      return false;
    }
    if (reportConfig.statusFilter.length > 0 && !reportConfig.statusFilter.includes(scope.status)) {
      return false;
    }
    return true;
  });

  const costSummary = {
    totalCost: filteredScopes.reduce((sum, scope) => sum + scope.totalCost, 0),
    completedCost: filteredScopes
      .filter(s => s.status === 'Completed')
      .reduce((sum, scope) => sum + scope.totalCost, 0),
    inProgressCost: filteredScopes
      .filter(s => s.status === 'In Progress')
      .reduce((sum, scope) => sum + scope.totalCost, 0),
    pendingCost: filteredScopes
      .filter(s => s.status === 'Pending' || s.status === 'Not Started')
      .reduce((sum, scope) => sum + scope.totalCost, 0)
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const selectedProject = mockProjects.find(p => p.id === reportConfig.projectId);

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Scopes & Bid Items Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <PreviewButton icon={<EyeOutlined />} onClick={handlePreview} loading={loading}>
            Preview
          </PreviewButton>
          <GenerateButton 
            type='primary' 
            icon={<DownloadOutlined />} 
            onClick={handleGenerate}
            loading={loading}
          >
            Generate Report
          </GenerateButton>
        </HeaderActions>
      </Header>

      <ConfigurationSection title="Report Configuration">
        <StyledForm
          form={form}
          layout="vertical"
          initialValues={{
            projectId: '',
            scopeFilter: [],
            statusFilter: [],
            exportFormat: 'pdf'
          }}
        >
          <FormRow>
            <Form.Item
              label="Project"
              name="projectId"
              rules={[{ required: true, message: 'Please select a project' }]}
            >
              <StyledSelect placeholder="Select project">
                {mockProjects.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.name}
                  </Select.Option>
                ))}
              </StyledSelect>
            </Form.Item>

            <Form.Item label="Scope Filter" name="scopeFilter">
              <StyledSelect mode="multiple" placeholder="Select scopes to include">
                {mockScopeData.map(scope => (
                  <Select.Option key={scope.id} value={scope.id}>
                    {scope.name}
                  </Select.Option>
                ))}
              </StyledSelect>
            </Form.Item>
          </FormRow>

          <FormRow>
            <Form.Item label="Status Filter" name="statusFilter">
              <StyledSelect mode="multiple" placeholder="Select statuses to include">
                <Select.Option value="Completed">Completed</Select.Option>
                <Select.Option value="In Progress">In Progress</Select.Option>
                <Select.Option value="Pending">Pending</Select.Option>
                <Select.Option value="Not Started">Not Started</Select.Option>
              </StyledSelect>
            </Form.Item>

            <Form.Item label="Export Format" name="exportFormat">
              <StyledSelect placeholder="Select export format">
                <Select.Option value="pdf">PDF</Select.Option>
                <Select.Option value="excel">Excel</Select.Option>
                <Select.Option value="csv">CSV</Select.Option>
              </StyledSelect>
            </Form.Item>
          </FormRow>
        </StyledForm>
      </ConfigurationSection>

      {showPreview && (
        <PreviewSection title="Report Preview">
          {loading ? (
            <LoadingContainer>
              <Spin size="large" />
            </LoadingContainer>
          ) : (
            <PreviewContent>
              <ReportHeader>
                <ReportTitle>Scopes & Bid Items Report</ReportTitle>
                <ReportSubtitle>
                  {selectedProject ? selectedProject.name : 'Project'} • 
                  Generated on {new Date().toLocaleDateString()}
                </ReportSubtitle>
              </ReportHeader>

              <CostSummarySection title="Cost Summary">
                <CostGrid>
                  <CostCard>
                    <CostValue>{formatCurrency(costSummary.totalCost)}</CostValue>
                    <CostLabel>Total Project Cost</CostLabel>
                  </CostCard>
                  <CostCard>
                    <CostValue>{formatCurrency(costSummary.completedCost)}</CostValue>
                    <CostLabel>Completed Work</CostLabel>
                  </CostCard>
                  <CostCard>
                    <CostValue>{formatCurrency(costSummary.inProgressCost)}</CostValue>
                    <CostLabel>In Progress</CostLabel>
                  </CostCard>
                  <CostCard>
                    <CostValue>{formatCurrency(costSummary.pendingCost)}</CostValue>
                    <CostLabel>Pending Work</CostLabel>
                  </CostCard>
                </CostGrid>
              </CostSummarySection>

              {filteredScopes.length > 0 ? (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 600, marginBottom: '20px' }}>
                    Scope Details
                  </h3>
                  {filteredScopes.map(scope => (
                    <ScopeCard key={scope.id} title={`${scope.division} - ${scope.name}`}>
                      <ScopeHeader>
                        <ScopeInfo>
                          <ScopeMeta>{scope.description}</ScopeMeta>
                          <StatusBadge status={scope.status}>{scope.status}</StatusBadge>
                        </ScopeInfo>
                      </ScopeHeader>

                      <ScopeMetrics>
                        <MetricItem>
                          <MetricValue>{formatCurrency(scope.totalCost)}</MetricValue>
                          <MetricLabel>Total Cost</MetricLabel>
                        </MetricItem>
                        <MetricItem>
                          <MetricValue>{scope.bidItemsCount}</MetricValue>
                          <MetricLabel>Bid Items</MetricLabel>
                        </MetricItem>
                        <MetricItem>
                          <MetricValue>{scope.progress}%</MetricValue>
                          <MetricLabel>Progress</MetricLabel>
                        </MetricItem>
                      </ScopeMetrics>

                      <BidItemsSection>
                        <BidItemsTitle>Bid Items</BidItemsTitle>
                        <StyledCollapse>
                          {scope.bidItems.map(bidItem => (
                            <StyledCollapse.Panel
                              key={bidItem.id}
                              header={
                                <BidItemHeader>
                                  <BidItemInfo>
                                    <BidItemName>{bidItem.name}</BidItemName>
                                    <BidItemMeta>
                                      {bidItem.quantity} {bidItem.unit} × {formatCurrency(bidItem.unitCost)}
                                    </BidItemMeta>
                                  </BidItemInfo>
                                  <BidItemCost>{formatCurrency(bidItem.totalCost)}</BidItemCost>
                                </BidItemHeader>
                              }
                            >
                              <BidItemDetails>
                                <DetailItem>
                                  <DetailLabel>Description</DetailLabel>
                                  <DetailValue>{bidItem.description}</DetailValue>
                                </DetailItem>
                                <DetailItem>
                                  <DetailLabel>Status</DetailLabel>
                                  <DetailValue>
                                    <StatusBadge status={bidItem.status}>{bidItem.status}</StatusBadge>
                                  </DetailValue>
                                </DetailItem>
                                <DetailItem>
                                  <DetailLabel>Quantity</DetailLabel>
                                  <DetailValue>{bidItem.quantity} {bidItem.unit}</DetailValue>
                                </DetailItem>
                                <DetailItem>
                                  <DetailLabel>Unit Cost</DetailLabel>
                                  <DetailValue>{formatCurrency(bidItem.unitCost)}</DetailValue>
                                </DetailItem>
                              </BidItemDetails>
                              
                              <SpecificationSection>
                                <SpecTitle>Specifications</SpecTitle>
                                <SpecContent>{bidItem.specifications}</SpecContent>
                              </SpecificationSection>
                            </StyledCollapse.Panel>
                          ))}
                        </StyledCollapse>
                      </BidItemsSection>
                    </ScopeCard>
                  ))}
                </div>
              ) : (
                <EmptyState>
                  <EmptyIcon>
                    <FileTextOutlined />
                  </EmptyIcon>
                  <EmptyText>No scopes found</EmptyText>
                  <EmptySubtext>Please select a project to view scopes and bid items</EmptySubtext>
                </EmptyState>
              )}
            </PreviewContent>
          )}
        </PreviewSection>
      )}
    </Container>
  );
};

export default ScopesBidItemsReport;
