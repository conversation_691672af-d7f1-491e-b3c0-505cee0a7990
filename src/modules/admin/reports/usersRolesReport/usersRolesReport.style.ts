import { Button, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

export const ExportButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StyledTable = styled(Table)`
  .ant-table-thead > tr > th {
    background-color: ${themeTokens.primaryColor};
    color: white;
    font-weight: 600;
    border-bottom: 1px solid ${themeTokens.primaryColor};
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f9f9f9;
  }

  .ant-pagination {
    margin-top: 24px;
    text-align: center;
  }
`;

export const StatusBadge = styled.span<{ active: boolean }>`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => props.active ? '#f6ffed' : '#fff2f0'};
  color: ${props => props.active ? '#52c41a' : '#ff4d4f'};
  border: 1px solid ${props => props.active ? '#b7eb8f' : '#ffccc7'};
`;

export const RoleBadge = styled.span<{ role: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 4px;
  margin-bottom: 4px;
  display: inline-block;
  background-color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#fff2f0';
      case 'manager':
        return '#f6ffed';
      case 'project manager':
        return '#e6f7ff';
      case 'user':
        return '#f9f0ff';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#ff4d4f';
      case 'manager':
        return '#52c41a';
      case 'project manager':
        return '#1890ff';
      case 'user':
        return '#722ed1';
      default:
        return '#666';
    }
  }};
  border: 1px solid ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#ffccc7';
      case 'manager':
        return '#b7eb8f';
      case 'project manager':
        return '#91d5ff';
      case 'user':
        return '#d3adf7';
      default:
        return '#d9d9d9';
    }
  }};
`;

export const ProjectInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const ProjectName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const ProjectNumber = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
`;

export const UserName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const UserEmail = styled.div`
  font-size: 13px;
  color: ${themeTokens.textGray};
  margin-top: 2px;
`;

export const RolesContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
`;

export const TableContainer = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

export const TableHeader = styled.div`
  padding: 20px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
`;

export const TableTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const TableDescription = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin: 4px 0 0 0;
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;
