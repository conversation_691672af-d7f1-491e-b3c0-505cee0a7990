import { Button, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

export const ExportButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StyledTable = styled(Table)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }
`;

export const StatusBadge = styled.span<{ active: boolean }>`
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: ${props => (props.active ? '#f6ffed' : '#fff2f0')};
  color: ${props => (props.active ? '#52c41a' : '#ff4d4f')};
  border: 1px solid ${props => (props.active ? '#b7eb8f' : '#ffccc7')};
`;

export const RoleBadge = styled.span<{ role: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 4px;
  margin-bottom: 4px;
  display: inline-block;
  background-color: #fff2f0;
  color: #ff4d4f;
  border: #ffccc7;
`;

export const ProjectInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const ProjectName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const ProjectNumber = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
`;

export const UserName = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
`;

export const UserEmail = styled.div`
  font-size: 13px;
  color: ${themeTokens.textGray};
  margin-top: 2px;
`;

export const RolesContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
`;

export const TableHeader = styled.div`
  padding: 20px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
`;

export const TableTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const TableDescription = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin: 4px 0 0 0;
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: ${themeTokens.textGray};
`;

export const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

export const EmptyText = styled.div`
  font-size: 16px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;
