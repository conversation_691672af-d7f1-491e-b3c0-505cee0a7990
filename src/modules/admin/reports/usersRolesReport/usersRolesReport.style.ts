import { But<PERSON>, Card, DatePicker, Form, Select } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

export const PreviewButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const GenerateButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const ConfigurationSection = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

export const StyledForm = styled(Form)`
  .ant-form-item-label > label {
    font-weight: 500;
    color: ${themeTokens.textBlack};
  }
`;

export const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0;
  }
`;

export const FilterSection = styled.div`
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
`;

export const FilterTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
`;

export const PreviewSection = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

export const PreviewContent = styled.div`
  min-height: 400px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 24px;
`;

export const ReportHeader = styled.div`
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
`;

export const ReportTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 8px;
`;

export const ReportSubtitle = styled.p`
  font-size: 14px;
  color: ${themeTokens.textGray};
  margin: 0;
`;

export const ReportMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
`;

export const MetaItem = styled.div`
  text-align: center;
`;

export const MetaLabel = styled.div`
  font-size: 12px;
  color: ${themeTokens.textGray};
  text-transform: uppercase;
  margin-bottom: 4px;
`;

export const MetaValue = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
`;

export const SummaryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

export const SummaryCard = styled.div`
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
`;

export const SummaryValue = styled.div`
  font-size: 28px;
  font-weight: 700;
  color: ${themeTokens.primaryColor};
  margin-bottom: 8px;
`;

export const SummaryLabel = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;

export const TableSection = styled.div`
  margin-top: 24px;
`;

export const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
`;

export const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;

  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
  }

  th {
    background: #fafafa;
    font-weight: 600;
    color: ${themeTokens.textBlack};
    font-size: 14px;
  }

  td {
    font-size: 14px;
    color: ${themeTokens.textBlack};
  }

  tr:hover {
    background: #f9f9f9;
  }
`;

export const RoleBadge = styled.span<{ role: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#fff2f0';
      case 'manager':
        return '#f6ffed';
      case 'user':
        return '#e6f7ff';
      default:
        return '#fafafa';
    }
  }};
  color: ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#ff4d4f';
      case 'manager':
        return '#52c41a';
      case 'user':
        return '#1890ff';
      default:
        return '#666';
    }
  }};
  border: 1px solid ${props => {
    switch (props.role.toLowerCase()) {
      case 'admin':
        return '#ffccc7';
      case 'manager':
        return '#b7eb8f';
      case 'user':
        return '#91d5ff';
      default:
        return '#d9d9d9';
    }
  }};
`;

export const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: ${props => props.status === 'Active' ? '#f6ffed' : '#fff2f0'};
  color: ${props => props.status === 'Active' ? '#52c41a' : '#ff4d4f'};
  border: 1px solid ${props => props.status === 'Active' ? '#b7eb8f' : '#ffccc7'};
`;

export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

export const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  color: ${themeTokens.textGray};
`;

export const StyledDatePicker = styled(DatePicker)`
  width: 100%;
`;

export const StyledSelect = styled(Select)`
  width: 100%;
`;
