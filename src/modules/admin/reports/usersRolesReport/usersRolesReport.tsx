import {
  ArrowLeftOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { Form, Select, DatePicker, Spin, notification } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { appRoutes } from '../../../utils/constant';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  HeaderLeft,
  BackButton,
  PageTitle,
  HeaderActions,
  PreviewButton,
  GenerateButton,
  ConfigurationSection,
  StyledForm,
  FormRow,
  FilterSection,
  FilterTitle,
  PreviewSection,
  PreviewContent,
  ReportHeader,
  ReportTitle,
  ReportSubtitle,
  ReportMeta,
  MetaItem,
  MetaLabel,
  MetaValue,
  SummaryGrid,
  SummaryCard,
  SummaryValue,
  SummaryLabel,
  TableSection,
  SectionTitle,
  StyledTable,
  RoleBadge,
  StatusBadge,
  LoadingContainer,
  EmptyState,
  StyledDatePicker,
  StyledSelect
} from './usersRolesReport.style';

const { RangePicker } = DatePicker;

interface UserData {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'Active' | 'Inactive';
  lastLogin: string;
  projectsAssigned: number;
  totalLogins: number;
}

interface ReportConfig {
  scope: 'company' | 'project';
  projectId?: string;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  includeInactive: boolean;
  roleFilter: string[];
  exportFormat: 'pdf' | 'excel' | 'csv';
}

const UsersRolesReport: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    scope: 'company',
    dateRange: [dayjs().subtract(30, 'days'), dayjs()],
    includeInactive: false,
    roleFilter: [],
    exportFormat: 'pdf'
  });

  // Mock data for demonstration
  const mockUserData: UserData[] = [
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Admin',
      status: 'Active',
      lastLogin: '2024-01-15',
      projectsAssigned: 5,
      totalLogins: 142
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'Manager',
      status: 'Active',
      lastLogin: '2024-01-14',
      projectsAssigned: 3,
      totalLogins: 89
    },
    {
      id: '3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'User',
      status: 'Active',
      lastLogin: '2024-01-13',
      projectsAssigned: 2,
      totalLogins: 56
    },
    {
      id: '4',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      role: 'User',
      status: 'Inactive',
      lastLogin: '2024-01-01',
      projectsAssigned: 1,
      totalLogins: 23
    }
  ];

  const mockProjects = [
    { id: '1', name: 'Office Building A' },
    { id: '2', name: 'Residential Complex B' },
    { id: '3', name: 'Shopping Mall C' }
  ];

  const handleBackClick = () => {
    navigate(`/${appRoutes.admin}/reports`);
  };

  const handlePreview = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      setReportConfig({ ...reportConfig, ...values });
      setShowPreview(true);
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // TODO: Implement actual report generation API call
      console.log('Generating report with config:', { ...reportConfig, ...values });
      
      notification.success({
        message: 'Report Generated',
        description: 'Your Users & Roles report has been generated and will be sent to your email.'
      });
    } catch (error) {
      notification.error({
        message: 'Generation Failed',
        description: 'Failed to generate report. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = mockUserData.filter(user => {
    if (!reportConfig.includeInactive && user.status === 'Inactive') {
      return false;
    }
    if (reportConfig.roleFilter.length > 0 && !reportConfig.roleFilter.includes(user.role)) {
      return false;
    }
    return true;
  });

  const summaryStats = {
    totalUsers: filteredUsers.length,
    activeUsers: filteredUsers.filter(u => u.status === 'Active').length,
    totalProjects: filteredUsers.reduce((sum, u) => sum + u.projectsAssigned, 0),
    avgLogins: Math.round(filteredUsers.reduce((sum, u) => sum + u.totalLogins, 0) / filteredUsers.length)
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Users & Roles Report</PageTitle>
        </HeaderLeft>
        <HeaderActions>
          <PreviewButton icon={<EyeOutlined />} onClick={handlePreview} loading={loading}>
            Preview
          </PreviewButton>
          <GenerateButton 
            type='primary' 
            icon={<DownloadOutlined />} 
            onClick={handleGenerate}
            loading={loading}
          >
            Generate Report
          </GenerateButton>
        </HeaderActions>
      </Header>

      <ConfigurationSection title="Report Configuration">
        <StyledForm
          form={form}
          layout="vertical"
          initialValues={{
            scope: 'company',
            dateRange: [dayjs().subtract(30, 'days'), dayjs()],
            includeInactive: false,
            roleFilter: [],
            exportFormat: 'pdf'
          }}
        >
          <FormRow>
            <Form.Item
              label="Report Scope"
              name="scope"
              rules={[{ required: true, message: 'Please select report scope' }]}
            >
              <StyledSelect placeholder="Select scope">
                <Select.Option value="company">Company Wide</Select.Option>
                <Select.Option value="project">Per Project</Select.Option>
              </StyledSelect>
            </Form.Item>

            <Form.Item
              label="Project"
              name="projectId"
              dependencies={['scope']}
              rules={[
                ({ getFieldValue }) => ({
                  required: getFieldValue('scope') === 'project',
                  message: 'Please select a project'
                })
              ]}
            >
              <StyledSelect 
                placeholder="Select project"
                disabled={form.getFieldValue('scope') !== 'project'}
              >
                {mockProjects.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.name}
                  </Select.Option>
                ))}
              </StyledSelect>
            </Form.Item>
          </FormRow>

          <FilterSection>
            <FilterTitle>Filters & Options</FilterTitle>
            <FormRow>
              <Form.Item label="Date Range" name="dateRange">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item label="Role Filter" name="roleFilter">
                <StyledSelect mode="multiple" placeholder="Select roles to include">
                  <Select.Option value="Admin">Admin</Select.Option>
                  <Select.Option value="Manager">Manager</Select.Option>
                  <Select.Option value="User">User</Select.Option>
                </StyledSelect>
              </Form.Item>
            </FormRow>

            <FormRow>
              <Form.Item label="Include Inactive Users" name="includeInactive" valuePropName="checked">
                <StyledSelect placeholder="Include inactive users">
                  <Select.Option value={true}>Yes</Select.Option>
                  <Select.Option value={false}>No</Select.Option>
                </StyledSelect>
              </Form.Item>

              <Form.Item label="Export Format" name="exportFormat">
                <StyledSelect placeholder="Select export format">
                  <Select.Option value="pdf">PDF</Select.Option>
                  <Select.Option value="excel">Excel</Select.Option>
                  <Select.Option value="csv">CSV</Select.Option>
                </StyledSelect>
              </Form.Item>
            </FormRow>
          </FilterSection>
        </StyledForm>
      </ConfigurationSection>

      {showPreview && (
        <PreviewSection title="Report Preview">
          {loading ? (
            <LoadingContainer>
              <Spin size="large" />
            </LoadingContainer>
          ) : (
            <PreviewContent>
              <ReportHeader>
                <ReportTitle>Users & Roles Report</ReportTitle>
                <ReportSubtitle>
                  Generated on {new Date().toLocaleDateString()} • 
                  {reportConfig.scope === 'company' ? 'Company Wide' : 'Project Specific'}
                </ReportSubtitle>
              </ReportHeader>

              <ReportMeta>
                <MetaItem>
                  <MetaLabel>Report Period</MetaLabel>
                  <MetaValue>
                    {reportConfig.dateRange ? 
                      `${reportConfig.dateRange[0].format('MMM DD')} - ${reportConfig.dateRange[1].format('MMM DD, YYYY')}` : 
                      'Last 30 Days'
                    }
                  </MetaValue>
                </MetaItem>
                <MetaItem>
                  <MetaLabel>Scope</MetaLabel>
                  <MetaValue>{reportConfig.scope === 'company' ? 'Company Wide' : 'Per Project'}</MetaValue>
                </MetaItem>
                <MetaItem>
                  <MetaLabel>Generated By</MetaLabel>
                  <MetaValue>Admin User</MetaValue>
                </MetaItem>
              </ReportMeta>

              <SummaryGrid>
                <SummaryCard>
                  <SummaryValue>{summaryStats.totalUsers}</SummaryValue>
                  <SummaryLabel>Total Users</SummaryLabel>
                </SummaryCard>
                <SummaryCard>
                  <SummaryValue>{summaryStats.activeUsers}</SummaryValue>
                  <SummaryLabel>Active Users</SummaryLabel>
                </SummaryCard>
                <SummaryCard>
                  <SummaryValue>{summaryStats.totalProjects}</SummaryValue>
                  <SummaryLabel>Projects Assigned</SummaryLabel>
                </SummaryCard>
                <SummaryCard>
                  <SummaryValue>{summaryStats.avgLogins}</SummaryValue>
                  <SummaryLabel>Avg. Logins</SummaryLabel>
                </SummaryCard>
              </SummaryGrid>

              <TableSection>
                <SectionTitle>User Details</SectionTitle>
                <StyledTable>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Last Login</th>
                      <th>Projects</th>
                      <th>Total Logins</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map(user => (
                      <tr key={user.id}>
                        <td>{user.name}</td>
                        <td>{user.email}</td>
                        <td><RoleBadge role={user.role}>{user.role}</RoleBadge></td>
                        <td><StatusBadge status={user.status}>{user.status}</StatusBadge></td>
                        <td>{formatDate(user.lastLogin)}</td>
                        <td>{user.projectsAssigned}</td>
                        <td>{user.totalLogins}</td>
                      </tr>
                    ))}
                  </tbody>
                </StyledTable>
              </TableSection>
            </PreviewContent>
          )}
        </PreviewSection>
      )}
    </Container>
  );
};

export default UsersRolesReport;
