import { ArrowLeftOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Button, Tag, Tooltip, Input, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserDTO } from 'src/api';
import { userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';

import {
  Container,
  Header,
  BackButton,
  PageTitle,
  GlobalSearchContainer,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  TooltipContent,
  EmailTooltipContent,
  GlobalSearchInput,
  StyledTable
} from './userManagement.style';
import { queryKeys, appRoutes } from '../../utils/constant';
import { formatDate } from '../../utils/util';

interface ExtendedUserDTO extends UserDTO {
  createdAt?: string;
}

const UserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Fetch all users across the platform with backend search and filtering
  const { data: usersData, isLoading } = useQuery({
    queryKey: [
      queryKeys.usersList,
      currentPage,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      userAPI.getAllUsers(
        currentPage - 1,
        pageSize,
        [`${sortField},${sortOrder}`],
        debouncedSearchText || columnFilters.name || undefined, // Name search (global + column filter)
        debouncedSearchText || columnFilters.email || undefined, // Email search (global + column filter)
        columnFilters.roleId || undefined, // Role ID filter
        columnFilters.companyId || undefined, // Company ID filter (will be updated when API supports it)
        columnFilters.isActive || undefined // Is Active filter
      ),
    select: response => response.data
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    if (filters) {
      const newFilters: Record<string, any> = {};
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilters[key] = filters[key][0]; // Take first filter value
        }
      });
      setColumnFilters(newFilters);
      setCurrentPage(1); // Reset to first page when filtering
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedUserDTO> = useMemo(
    () => [
      {
        title: 'User ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        width: 250,
        sorter: true,
        ...getColumnSearchProps('email', 'email'),
        render: (email: string) => (
          <Tooltip title={email}>
            <EmailTooltipContent>{email || '-'}</EmailTooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <TooltipContent>{companyName || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Active User',
        dataIndex: 'isActive',
        key: 'isActive',
        width: 120,
        sorter: true,
        ...getColumnSelectProps('isActive', [
          { text: 'Active', value: 'true' },
          { text: 'Inactive', value: 'false' }
        ]),
        render: (isActive: boolean) => (
          <Tag color={isActive ? themeTokens.successGreen : themeTokens.dangerRed}>
            {isActive ? 'Yes' : 'No'}
          </Tag>
        )
      },
      {
        title: 'Create Time',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Current Role',
        dataIndex: ['role', 'name'],
        key: 'role',
        width: 150,
        render: (roleName: string) => <Tag color={themeTokens.infoBlue}>{roleName || '-'}</Tag>
      },
      {
        title: 'Last Login',
        dataIndex: 'lastLoginDate',
        key: 'lastLoginDate',
        width: 150,
        render: (lastLoginDate: string) => (lastLoginDate ? formatDate(lastLoginDate) : '-')
      }
    ],
    [getColumnSearchProps, getColumnSelectProps]
  );

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>User Management</PageTitle>
      </Header>

      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all users...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
      </GlobalSearchContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={usersData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: usersData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1200 }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default UserManagement;
