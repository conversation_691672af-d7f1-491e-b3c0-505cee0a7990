import { ArrowLeftOutlined, BuildOutlined, UserOutlined } from '@ant-design/icons';
import { Col, Statistic, Spin } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { themeTokens } from 'src/theme/tokens';
import {
  <PERSON><PERSON>er,
  Header,
  BackButton,
  PageTitle,
  SectionTitle,
  BaseMetricCard,
  LoadingContainer,
  ProjectIcon,
  MetricRow
} from './metrics.style';
import projectIcon from '../../../assets/images/project.svg';
import { appRoutes } from '../../utils/constant';

// Types
interface MetricItem {
  title: string;
  value: number;
}

interface MetricSection {
  title: string;
  icon: React.ReactNode;
  color: string;
  items: MetricItem[];
}

const Metrics: React.FC = () => {
  const navigate = useNavigate();

  // TODO: Replace with actual API calls when metrics endpoints are available
  // For now, using mock data
  const isLoading = false; // Set to true when implementing actual API calls

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  // Define metric sections with their data
  const metricSections: MetricSection[] = [
    {
      title: 'Companies',
      icon: <BuildOutlined style={{ color: themeTokens.successGreen }} />,
      color: themeTokens.successGreen,
      items: [
        { title: 'Total Companies', value: 45 },
        { title: 'Enterprise Companies', value: 12 },
        { title: 'Non-Enterprise Companies', value: 33 }
      ]
    },
    {
      title: 'Projects',
      icon: <ProjectIcon src={projectIcon} alt='Projects' />,
      color: themeTokens.infoBlue,
      items: [
        { title: 'Total Projects', value: 128 },
        { title: 'Paid Projects', value: 89 },
        { title: 'Unpaid Projects', value: 39 },
        { title: 'Projects in Bidding Phase', value: 23 },
        { title: 'Projects Closed (Won)', value: 67 },
        { title: 'Projects Closed (Lost)', value: 38 }
      ]
    },
    {
      title: 'Users',
      icon: <UserOutlined style={{ color: themeTokens.warningOrange }} />,
      color: themeTokens.warningOrange,
      items: [
        { title: 'Total Users', value: 234 },
        { title: 'Active Users', value: 198 },
        { title: 'Inactive Users', value: 36 }
      ]
    }
  ];

  if (isLoading) {
    return (
      <Container>
        <Header>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Metrics Dashboard</PageTitle>
        </Header>
        <LoadingContainer>
          <Spin size='large' />
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Metrics Dashboard</PageTitle>
      </Header>
      {metricSections.map((section, index) => (
        <>
          <SectionTitle>
            {section.icon}
            {section.title}
          </SectionTitle>
          <MetricRow
            gutter={[24, 24]}
            style={index === metricSections.length - 1 ? { marginBottom: 0 } : undefined}
          >
            {section.items.map((item, index) => (
              <Col xs={24} sm={8} key={index}>
                <BaseMetricCard borderColor={section.color} textColor={section.color}>
                  <Statistic title={item.title} value={item.value} />
                </BaseMetricCard>
              </Col>
            ))}
          </MetricRow>
        </>
      ))}
    </Container>
  );
};

export default Metrics;
