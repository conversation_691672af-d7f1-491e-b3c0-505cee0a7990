import { <PERSON><PERSON>, <PERSON>, Row } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const SectionTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const BaseMetricCard = styled(Card)<{ borderColor: string; textColor: string }>`
  height: 140px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid ${props => props.borderColor};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .ant-card-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }

  .ant-statistic-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    font-size: 28px;
    font-weight: 700;
    color: ${props => props.textColor};
  }
`;

export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`;

export const ProjectIcon = styled.img`
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(47%) sepia(96%) saturate(1237%) hue-rotate(195deg)
    brightness(98%) contrast(98%);
`;

export const MetricRow = styled(Row)`
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }
`;
