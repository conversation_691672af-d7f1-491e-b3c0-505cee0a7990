import { Button, Card, Form, Input, Select, Switch, Tabs } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 16px;
`;

export const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

export const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 24px;
  }
  
  .ant-tabs-tab {
    font-size: 16px;
    font-weight: 500;
  }
  
  .ant-tabs-tab-active {
    font-weight: 600;
  }
`;

export const TabContent = styled.div`
  padding: 0;
`;

export const SectionCard = styled(Card)`
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-card-head-title {
    font-size: 18px;
    font-weight: 600;
    color: ${themeTokens.textBlack};
  }
`;

export const FormSection = styled.div`
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

export const SectionTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
`;

export const StyledForm = styled(Form)`
  .ant-form-item-label > label {
    font-weight: 500;
    color: ${themeTokens.textBlack};
  }
  
  .ant-form-item {
    margin-bottom: 20px;
  }
`;

export const FormRow = styled.div`
  display: flex;
  gap: 24px;
  
  .ant-form-item {
    flex: 1;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }
`;

export const SwitchContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
`;

export const SwitchLabel = styled.div`
  flex: 1;
`;

export const SwitchTitle = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const SwitchDescription = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;

export const StyledSwitch = styled(Switch)`
  margin-left: 16px;
`;

export const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
`;

export const SaveButton = styled(Button)`
  min-width: 120px;
`;

export const CancelButton = styled(Button)`
  min-width: 120px;
`;

export const ExportSection = styled.div`
  margin-bottom: 32px;
`;

export const ExportCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
  }
`;

export const ExportDescription = styled.p`
  color: ${themeTokens.textGray};
  margin-bottom: 16px;
`;

export const ExportOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
`;

export const ExportOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
`;

export const ExportOptionContent = styled.div`
  flex: 1;
`;

export const ExportOptionTitle = styled.div`
  font-weight: 500;
  color: ${themeTokens.textBlack};
  margin-bottom: 4px;
`;

export const ExportOptionDescription = styled.div`
  font-size: 14px;
  color: ${themeTokens.textGray};
`;

export const ExportButton = styled(Button)`
  margin-left: 16px;
`;

export const ScheduleSection = styled.div`
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 6px;
  margin-top: 24px;
`;

export const ScheduleTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin-bottom: 16px;
`;
