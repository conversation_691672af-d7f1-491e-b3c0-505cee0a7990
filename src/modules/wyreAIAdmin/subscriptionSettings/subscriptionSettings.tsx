import {
  ArrowLeftOutlined,
  DownloadOutlined,
  SettingOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { Form, Input, Select, Switch, notification, TimePicker } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { appRoutes } from '../../utils/constant';
import {
  Con<PERSON>er,
  <PERSON>er,
  BackButton,
  PageTitle,
  StyledTabs,
  TabContent,
  SectionCard,
  FormSection,
  SectionTitle,
  StyledForm,
  FormRow,
  SwitchContainer,
  SwitchLabel,
  SwitchTitle,
  SwitchDescription,
  StyledSwitch,
  ActionButtons,
  SaveButton,
  CancelButton,
  ExportSection,
  ExportCard,
  ExportDescription,
  ExportOptions,
  ExportOption,
  ExportOptionContent,
  ExportOptionTitle,
  ExportOptionDescription,
  ExportButton,
  ScheduleSection,
  ScheduleTitle
} from './subscriptionSettings.style';

const SubscriptionSettings: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('company-settings');
  const [loading, setLoading] = useState(false);

  // Company Settings State
  const [companySettings, setCompanySettings] = useState({
    allowProjectSharing: true,
    enableNotifications: true,
    autoBackup: false,
    dataRetentionDays: 365,
    maxUsers: 50,
    maxProjects: 100,
    enableApiAccess: true,
    enableSSOLogin: false,
    requireTwoFactor: false,
    allowGuestAccess: true
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // TODO: Implement actual API call to save settings
      console.log('Saving settings:', { ...companySettings, ...values });

      notification.success({
        message: 'Settings Saved',
        description: 'Company settings have been updated successfully.'
      });
    } catch (error) {
      notification.error({
        message: 'Save Failed',
        description: 'Failed to save settings. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSettings = () => {
    form.resetFields();
    // Reset to original values
    setCompanySettings({
      allowProjectSharing: true,
      enableNotifications: true,
      autoBackup: false,
      dataRetentionDays: 365,
      maxUsers: 50,
      maxProjects: 100,
      enableApiAccess: true,
      enableSSOLogin: false,
      requireTwoFactor: false,
      allowGuestAccess: true
    });
  };

  const handleExport = (exportType: string) => {
    notification.success({
      message: 'Export Started',
      description: `${exportType} export has been initiated. You will receive an email when it's ready.`
    });
  };

  const handleSwitchChange = (key: string, value: boolean) => {
    setCompanySettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Company Settings Tab Content
  const CompanySettingsTab = () => (
    <TabContent>
      <SectionCard title='General Settings'>
        <StyledForm
          form={form}
          layout='vertical'
          initialValues={{
            companyName: 'Acme Corporation',
            adminEmail: '<EMAIL>',
            timezone: 'America/New_York',
            currency: 'USD',
            dataRetentionDays: companySettings.dataRetentionDays,
            maxUsers: companySettings.maxUsers,
            maxProjects: companySettings.maxProjects
          }}
        >
          <FormSection>
            <SectionTitle>Company Information</SectionTitle>
            <FormRow>
              <Form.Item
                label='Company Name'
                name='companyName'
                rules={[{ required: true, message: 'Please enter company name' }]}
              >
                <Input placeholder='Enter company name' />
              </Form.Item>
              <Form.Item
                label='Admin Email'
                name='adminEmail'
                rules={[
                  { required: true, message: 'Please enter admin email' },
                  { type: 'email', message: 'Please enter a valid email' }
                ]}
              >
                <Input placeholder='Enter admin email' />
              </Form.Item>
            </FormRow>
            <FormRow>
              <Form.Item
                label='Timezone'
                name='timezone'
                rules={[{ required: true, message: 'Please select timezone' }]}
              >
                <Select placeholder='Select timezone'>
                  <Select.Option value='America/New_York'>Eastern Time (ET)</Select.Option>
                  <Select.Option value='America/Chicago'>Central Time (CT)</Select.Option>
                  <Select.Option value='America/Denver'>Mountain Time (MT)</Select.Option>
                  <Select.Option value='America/Los_Angeles'>Pacific Time (PT)</Select.Option>
                  <Select.Option value='UTC'>UTC</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                label='Currency'
                name='currency'
                rules={[{ required: true, message: 'Please select currency' }]}
              >
                <Select placeholder='Select currency'>
                  <Select.Option value='USD'>USD - US Dollar</Select.Option>
                  <Select.Option value='EUR'>EUR - Euro</Select.Option>
                  <Select.Option value='GBP'>GBP - British Pound</Select.Option>
                  <Select.Option value='CAD'>CAD - Canadian Dollar</Select.Option>
                </Select>
              </Form.Item>
            </FormRow>
          </FormSection>

          <FormSection>
            <SectionTitle>Subscription Limits</SectionTitle>
            <FormRow>
              <Form.Item
                label='Maximum Users'
                name='maxUsers'
                rules={[{ required: true, message: 'Please enter maximum users' }]}
              >
                <Input type='number' placeholder='Enter maximum users' />
              </Form.Item>
              <Form.Item
                label='Maximum Projects'
                name='maxProjects'
                rules={[{ required: true, message: 'Please enter maximum projects' }]}
              >
                <Input type='number' placeholder='Enter maximum projects' />
              </Form.Item>
            </FormRow>
            <FormRow>
              <Form.Item
                label='Data Retention (Days)'
                name='dataRetentionDays'
                rules={[{ required: true, message: 'Please enter data retention period' }]}
              >
                <Input type='number' placeholder='Enter retention period in days' />
              </Form.Item>
            </FormRow>
          </FormSection>
        </StyledForm>
      </SectionCard>

      <SectionCard title='Feature Settings'>
        <FormSection>
          <SectionTitle>Access & Permissions</SectionTitle>
          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Project Sharing</SwitchTitle>
              <SwitchDescription>
                Allow users to share projects with external collaborators
              </SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.allowProjectSharing}
              onChange={value => handleSwitchChange('allowProjectSharing', value)}
            />
          </SwitchContainer>

          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Guest Access</SwitchTitle>
              <SwitchDescription>Allow guest users to view shared projects</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.allowGuestAccess}
              onChange={value => handleSwitchChange('allowGuestAccess', value)}
            />
          </SwitchContainer>

          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>API Access</SwitchTitle>
              <SwitchDescription>Enable API access for third-party integrations</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.enableApiAccess}
              onChange={value => handleSwitchChange('enableApiAccess', value)}
            />
          </SwitchContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>Security Settings</SectionTitle>
          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Single Sign-On (SSO)</SwitchTitle>
              <SwitchDescription>Enable SSO login for company users</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.enableSSOLogin}
              onChange={value => handleSwitchChange('enableSSOLogin', value)}
            />
          </SwitchContainer>

          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Two-Factor Authentication</SwitchTitle>
              <SwitchDescription>Require 2FA for all user accounts</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.requireTwoFactor}
              onChange={value => handleSwitchChange('requireTwoFactor', value)}
            />
          </SwitchContainer>
        </FormSection>

        <FormSection>
          <SectionTitle>System Settings</SectionTitle>
          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Email Notifications</SwitchTitle>
              <SwitchDescription>Send email notifications for important events</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.enableNotifications}
              onChange={value => handleSwitchChange('enableNotifications', value)}
            />
          </SwitchContainer>

          <SwitchContainer>
            <SwitchLabel>
              <SwitchTitle>Automatic Backup</SwitchTitle>
              <SwitchDescription>Enable automatic daily backups of company data</SwitchDescription>
            </SwitchLabel>
            <StyledSwitch
              checked={companySettings.autoBackup}
              onChange={value => handleSwitchChange('autoBackup', value)}
            />
          </SwitchContainer>
        </FormSection>

        <ActionButtons>
          <CancelButton onClick={handleCancelSettings}>Cancel</CancelButton>
          <SaveButton type='primary' loading={loading} onClick={handleSaveSettings}>
            Save Settings
          </SaveButton>
        </ActionButtons>
      </SectionCard>
    </TabContent>
  );

  // Company Export Options Tab Content
  const CompanyExportTab = () => (
    <TabContent>
      <ExportSection>
        <ExportCard title='Data Export Options'>
          <ExportDescription>
            Export company data for backup, compliance, or migration purposes. All exports are
            encrypted and will be sent to your registered email address.
          </ExportDescription>

          <ExportOptions>
            <ExportOption>
              <ExportOptionContent>
                <ExportOptionTitle>Complete Company Data</ExportOptionTitle>
                <ExportOptionDescription>
                  Export all company data including projects, users, scopes, and settings
                </ExportOptionDescription>
              </ExportOptionContent>
              <ExportButton
                type='primary'
                icon={<DownloadOutlined />}
                onClick={() => handleExport('Complete Company Data')}
              >
                Export All
              </ExportButton>
            </ExportOption>

            <ExportOption>
              <ExportOptionContent>
                <ExportOptionTitle>Projects Data Only</ExportOptionTitle>
                <ExportOptionDescription>
                  Export all projects, scopes, and related documents
                </ExportOptionDescription>
              </ExportOptionContent>
              <ExportButton
                icon={<DownloadOutlined />}
                onClick={() => handleExport('Projects Data')}
              >
                Export Projects
              </ExportButton>
            </ExportOption>

            <ExportOption>
              <ExportOptionContent>
                <ExportOptionTitle>Users & Permissions</ExportOptionTitle>
                <ExportOptionDescription>
                  Export user accounts, roles, and permission settings
                </ExportOptionDescription>
              </ExportOptionContent>
              <ExportButton
                icon={<DownloadOutlined />}
                onClick={() => handleExport('Users & Permissions')}
              >
                Export Users
              </ExportButton>
            </ExportOption>

            <ExportOption>
              <ExportOptionContent>
                <ExportOptionTitle>Activity Logs</ExportOptionTitle>
                <ExportOptionDescription>
                  Export system activity logs and audit trail
                </ExportOptionDescription>
              </ExportOptionContent>
              <ExportButton
                icon={<DownloadOutlined />}
                onClick={() => handleExport('Activity Logs')}
              >
                Export Logs
              </ExportButton>
            </ExportOption>

            <ExportOption>
              <ExportOptionContent>
                <ExportOptionTitle>Financial Data</ExportOptionTitle>
                <ExportOptionDescription>
                  Export subscription, billing, and payment information
                </ExportOptionDescription>
              </ExportOptionContent>
              <ExportButton
                icon={<DownloadOutlined />}
                onClick={() => handleExport('Financial Data')}
              >
                Export Financial
              </ExportButton>
            </ExportOption>
          </ExportOptions>
        </ExportCard>
      </ExportSection>

      <ExportSection>
        <ExportCard title='Scheduled Exports'>
          <ExportDescription>
            Configure automatic data exports for regular backups and compliance requirements.
          </ExportDescription>

          <ScheduleSection>
            <ScheduleTitle>Automatic Export Schedule</ScheduleTitle>
            <StyledForm layout='vertical'>
              <FormRow>
                <Form.Item label='Export Frequency' name='frequency'>
                  <Select placeholder='Select frequency' defaultValue='weekly'>
                    <Select.Option value='daily'>Daily</Select.Option>
                    <Select.Option value='weekly'>Weekly</Select.Option>
                    <Select.Option value='monthly'>Monthly</Select.Option>
                    <Select.Option value='quarterly'>Quarterly</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label='Export Time' name='exportTime'>
                  <TimePicker
                    format='HH:mm'
                    defaultValue={dayjs('02:00', 'HH:mm')}
                    placeholder='Select time'
                  />
                </Form.Item>
              </FormRow>
              <FormRow>
                <Form.Item label='Export Type' name='exportType'>
                  <Select placeholder='Select export type' defaultValue='complete'>
                    <Select.Option value='complete'>Complete Company Data</Select.Option>
                    <Select.Option value='projects'>Projects Only</Select.Option>
                    <Select.Option value='users'>Users & Permissions</Select.Option>
                    <Select.Option value='logs'>Activity Logs</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label='Retention Period' name='retentionDays'>
                  <Select placeholder='Select retention' defaultValue='30'>
                    <Select.Option value='7'>7 Days</Select.Option>
                    <Select.Option value='30'>30 Days</Select.Option>
                    <Select.Option value='90'>90 Days</Select.Option>
                    <Select.Option value='365'>1 Year</Select.Option>
                  </Select>
                </Form.Item>
              </FormRow>

              <SwitchContainer>
                <SwitchLabel>
                  <SwitchTitle>Enable Scheduled Exports</SwitchTitle>
                  <SwitchDescription>
                    Automatically export data based on the schedule above
                  </SwitchDescription>
                </SwitchLabel>
                <StyledSwitch defaultChecked={false} />
              </SwitchContainer>

              <ActionButtons>
                <CancelButton>Cancel</CancelButton>
                <SaveButton type='primary'>Save Schedule</SaveButton>
              </ActionButtons>
            </StyledForm>
          </ScheduleSection>
        </ExportCard>
      </ExportSection>
    </TabContent>
  );

  const tabItems = [
    {
      key: 'company-settings',
      label: (
        <span>
          <SettingOutlined />
          Company Settings
        </span>
      ),
      children: <CompanySettingsTab />
    },
    {
      key: 'export-options',
      label: (
        <span>
          <ExportOutlined />
          Company Export Options
        </span>
      ),
      children: <CompanyExportTab />
    }
  ];

  return (
    <Container>
      <Header>
        <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
          Back
        </BackButton>
        <PageTitle>Subscription Settings</PageTitle>
      </Header>

      <StyledTabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} size='large' />
    </Container>
  );
};

export default SubscriptionSettings;
