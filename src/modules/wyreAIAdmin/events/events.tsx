import {
  ArrowLeftOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Button, Tooltip, Select, notification } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { themeTokens } from 'src/theme/tokens';

import { queryKeys, appRoutes } from '../../utils/constant';
import { formatDate } from '../../utils/util';
import {
  Container,
  Header,
  HeaderLeft,
  BackButton,
  PageTitle,
  ExportButton,
  FiltersContainer,
  FilterGroup,
  FilterLabel,
  GlobalSearchInput,
  FilterSelect,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  <PERSON>lt<PERSON><PERSON>ontent,
  StatusTag,
  UserTypeTag,
  StyledTable
} from './events.style';

// Types for Event data
interface EventDTO {
  id: number;
  userName: string;
  userEmail: string;
  userType: 'Paid' | 'Unpaid';
  moduleName: string;
  actionType: string;
  status: 'Success' | 'Failed' | 'Pending';
  description: string;
  companyName: string;
  projectName?: string;
  createdTime: string;
  ipAddress: string;
}

const Events: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdTime');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});

  // Filter states
  const [userTypeFilter, setUserTypeFilter] = useState<string | undefined>(undefined);
  const [moduleFilter, setModuleFilter] = useState<string | undefined>(undefined);
  const [actionTypeFilter, setActionTypeFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Mock data for now - replace with actual API call
  const mockEvents: EventDTO[] = [
    {
      id: 1,
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      userType: 'Paid',
      moduleName: 'Projects',
      actionType: 'Create',
      status: 'Success',
      description: 'Created new project "Office Building A"',
      companyName: 'ABC Construction',
      projectName: 'Office Building A',
      createdTime: '2024-01-15T10:30:00Z',
      ipAddress: '*************'
    },
    {
      id: 2,
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      userType: 'Unpaid',
      moduleName: 'Scopes',
      actionType: 'Update',
      status: 'Success',
      description: 'Updated scope "Electrical Work"',
      companyName: 'XYZ Engineering',
      projectName: 'Residential Complex',
      createdTime: '2024-01-15T09:15:00Z',
      ipAddress: '*************'
    },
    {
      id: 3,
      userName: 'Mike Johnson',
      userEmail: '<EMAIL>',
      userType: 'Paid',
      moduleName: 'Users',
      actionType: 'Login',
      status: 'Success',
      description: 'User logged into the system',
      companyName: 'DEF Contractors',
      createdTime: '2024-01-15T08:45:00Z',
      ipAddress: '*************'
    }
  ];

  // Simulate API call with mock data
  const { data: eventsData, isLoading } = useQuery({
    queryKey: [
      queryKeys.events,
      currentPage,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      userTypeFilter,
      moduleFilter,
      actionTypeFilter,
      statusFilter
    ],
    queryFn: () => {
      // Simulate API delay
      return new Promise(resolve => {
        setTimeout(() => {
          let filteredEvents = [...mockEvents];

          // Apply filters
          if (debouncedSearchText) {
            filteredEvents = filteredEvents.filter(
              event =>
                event.userName.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
                event.userEmail.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
                event.description.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
                event.companyName.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
                event.projectName?.toLowerCase().includes(debouncedSearchText.toLowerCase())
            );
          }

          if (userTypeFilter) {
            filteredEvents = filteredEvents.filter(event => event.userType === userTypeFilter);
          }

          if (moduleFilter) {
            filteredEvents = filteredEvents.filter(event => event.moduleName === moduleFilter);
          }

          if (actionTypeFilter) {
            filteredEvents = filteredEvents.filter(event => event.actionType === actionTypeFilter);
          }

          if (statusFilter) {
            filteredEvents = filteredEvents.filter(event => event.status === statusFilter);
          }

          // Apply sorting
          filteredEvents.sort((a, b) => {
            const aValue = a[sortField as keyof EventDTO];
            const bValue = b[sortField as keyof EventDTO];
            const comparison = String(aValue).localeCompare(String(bValue));
            return sortOrder === 'asc' ? comparison : -comparison;
          });

          // Apply pagination
          const startIndex = (currentPage - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

          resolve({
            content: paginatedEvents,
            totalElements: filteredEvents.length
          });
        }, 500);
      });
    },
    select: (response: any) => response
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1);
  };

  const handleExport = () => {
    // TODO: Implement actual export functionality
    notification.success({
      message: 'Export Started',
      description:
        "Your activity log export has been initiated. You will receive an email when it's ready."
    });
  };

  const clearAllFilters = () => {
    setGlobalSearchText('');
    setUserTypeFilter(undefined);
    setModuleFilter(undefined);
    setActionTypeFilter(undefined);
    setStatusFilter(undefined);
    setCurrentPage(1);
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      )
    }),
    []
  );

  const columns: ColumnsType<EventDTO> = useMemo(
    () => [
      {
        title: 'User Name',
        dataIndex: 'userName',
        key: 'userName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('userName', 'user name'),
        render: (userName: string) => (
          <Tooltip title={userName}>
            <TooltipContent>{userName}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'User Email',
        dataIndex: 'userEmail',
        key: 'userEmail',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('userEmail', 'email'),
        render: (userEmail: string) => (
          <Tooltip title={userEmail}>
            <TooltipContent>{userEmail}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'User Type',
        dataIndex: 'userType',
        key: 'userType',
        width: 100,
        sorter: true,
        render: (userType: string) => <UserTypeTag userType={userType}>{userType}</UserTypeTag>
      },
      {
        title: 'Module',
        dataIndex: 'moduleName',
        key: 'moduleName',
        width: 120,
        sorter: true,
        ...getColumnSearchProps('moduleName', 'module'),
        render: (moduleName: string) => moduleName
      },
      {
        title: 'Action',
        dataIndex: 'actionType',
        key: 'actionType',
        width: 100,
        sorter: true,
        render: (actionType: string) => actionType
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        sorter: true,
        render: (status: string) => <StatusTag status={status}>{status}</StatusTag>
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
        width: 250,
        ...getColumnSearchProps('description', 'description'),
        render: (description: string) => (
          <Tooltip title={description}>
            <TooltipContent>{description}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <TooltipContent>{companyName}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project',
        dataIndex: 'projectName',
        key: 'projectName',
        width: 150,
        render: (projectName: string) => projectName || '-'
      },
      {
        title: 'Created Time',
        dataIndex: 'createdTime',
        key: 'createdTime',
        width: 150,
        sorter: true,
        render: (createdTime: string) => formatDate(createdTime)
      },
      {
        title: 'IP Address',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        width: 120,
        render: (ipAddress: string) => ipAddress
      }
    ],
    [getColumnSearchProps]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Events & Activity Log</PageTitle>
        </HeaderLeft>
        <ExportButton type='primary' icon={<DownloadOutlined />} onClick={handleExport}>
          Export Log
        </ExportButton>
      </Header>

      <FiltersContainer>
        <FilterGroup>
          <FilterLabel>Search</FilterLabel>
          <GlobalSearchInput
            placeholder='Search events...'
            allowClear
            enterButton={<SearchOutlined />}
            size='large'
            onSearch={handleGlobalSearch}
            onChange={e => {
              if (!e.target.value) {
                handleGlobalSearch('');
              }
            }}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>User Type</FilterLabel>
          <FilterSelect
            placeholder='All User Types'
            allowClear
            value={userTypeFilter}
            onChange={setUserTypeFilter}
          >
            <Select.Option value='Paid'>Paid</Select.Option>
            <Select.Option value='Unpaid'>Unpaid</Select.Option>
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Module</FilterLabel>
          <FilterSelect
            placeholder='All Modules'
            allowClear
            value={moduleFilter}
            onChange={setModuleFilter}
          >
            <Select.Option value='Projects'>Projects</Select.Option>
            <Select.Option value='Scopes'>Scopes</Select.Option>
            <Select.Option value='Users'>Users</Select.Option>
            <Select.Option value='Companies'>Companies</Select.Option>
            <Select.Option value='Subscriptions'>Subscriptions</Select.Option>
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Action Type</FilterLabel>
          <FilterSelect
            placeholder='All Actions'
            allowClear
            value={actionTypeFilter}
            onChange={setActionTypeFilter}
          >
            <Select.Option value='Create'>Create</Select.Option>
            <Select.Option value='Update'>Update</Select.Option>
            <Select.Option value='Delete'>Delete</Select.Option>
            <Select.Option value='Login'>Login</Select.Option>
            <Select.Option value='Logout'>Logout</Select.Option>
            <Select.Option value='Export'>Export</Select.Option>
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Status</FilterLabel>
          <FilterSelect
            placeholder='All Status'
            allowClear
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <Select.Option value='Success'>Success</Select.Option>
            <Select.Option value='Failed'>Failed</Select.Option>
            <Select.Option value='Pending'>Pending</Select.Option>
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>&nbsp;</FilterLabel>
          <Button onClick={clearAllFilters}>Clear All Filters</Button>
        </FilterGroup>
      </FiltersContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={eventsData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: eventsData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} events`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1600 }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default Events;
