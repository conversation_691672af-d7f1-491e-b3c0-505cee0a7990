import { ArrowLeftOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Button, Tag, Tooltip, Input } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CompanyDTO } from 'src/api';
import { companyAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

import { queryKeys, appRoutes } from '../../utils/constant';
import { formatDate } from '../../utils/util';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
`;

const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const FilterButtonContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const FilterButton = styled(Button)`
  width: 90px;
`;

const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

const StyledTable = styled(Table<ExtendedCompanyDTO>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /*Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

interface ExtendedCompanyDTO extends CompanyDTO {
  userCount?: number;
  projectCount?: number;
  lastActivity?: string;
}

const CompanyManagement: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchText]);

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  // Fetch companies data
  const { data: companiesData, isLoading } = useQuery({
    queryKey: [
      queryKeys.companiesList,
      currentPage - 1,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      companyAPI.getAllCompanies(
        currentPage - 1,
        pageSize,
        sortField && sortOrder ? [`${sortField},${sortOrder}`] : undefined,
        debouncedSearchText || undefined,
        columnFilters.industry || undefined,
        columnFilters.subscriptionStatus || undefined
      ),
    select: response => response.data
  });

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    if (filters) {
      const newFilters: Record<string, any> = {};
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilters[key] = filters[key][0]; // Take first filter value
        }
      });
      setColumnFilters(newFilters);
      setCurrentPage(1); // Reset to first page when filtering
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getSubscriptionStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return themeTokens.successGreen;
      case 'TRIAL':
        return themeTokens.warningOrange;
      case 'SUSPENDED':
        return themeTokens.dangerRed;
      default:
        return themeTokens.textGray;
    }
  };

  const columns: ColumnsType<ExtendedCompanyDTO> = useMemo(
    () => [
      {
        title: 'Company ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true
      },
      {
        title: 'Company Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'company name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Industry',
        dataIndex: 'industry',
        key: 'industry',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('industry', 'industry'),
        render: (industry: string) => industry || '-'
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 150,
        ...getColumnSelectProps('subscriptionStatus', [
          { text: 'Active', value: 'ACTIVE' },
          { text: 'Trial', value: 'TRIAL' },
          { text: 'Suspended', value: 'SUSPENDED' }
        ]),
        render: (status: string) => (
          <Tag color={getSubscriptionStatusColor(status)}>{status || 'Unknown'}</Tag>
        )
      },
      {
        title: 'Primary Domain',
        dataIndex: 'primaryDomain',
        key: 'primaryDomain',
        width: 180,
        render: (domain: string) => (
          <Tooltip title={domain}>
            <TooltipContent>{domain || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Users',
        dataIndex: 'userCount',
        key: 'userCount',
        width: 80,
        render: (userCount: number) => userCount || 0
      },
      {
        title: 'Projects',
        dataIndex: 'projectCount',
        key: 'projectCount',
        width: 80,
        render: (projectCount: number) => projectCount || 0
      },
      {
        title: 'Last Activity',
        dataIndex: 'lastActivity',
        key: 'lastActivity',
        width: 120,
        render: (lastActivity: string) => (lastActivity ? formatDate(lastActivity) : '-')
      }
    ],
    [getColumnSearchProps, getColumnSelectProps]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Company Management</PageTitle>
        </HeaderLeft>
      </Header>

      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all companies...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
      </GlobalSearchContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={companiesData?.content || []}
          rowKey='id'
          loading={isLoading}
          onChange={handleTableChange}
          rowClassName={() => 'editable-row'}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: companiesData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} companies`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1200 }}
        />
      </StyledTableContainer>
    </Container>
  );
};

export default CompanyManagement;
