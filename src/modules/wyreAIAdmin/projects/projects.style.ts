import { Button, Input, Space, Table } from 'antd';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

const Container = styled.div`
  padding: 24px;
  min-height: calc(100vh - 60px);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: ${themeTokens.textBlack};
  margin: 0;
`;

const GlobalSearchContainer = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
`;

const StyledTableContainer = styled.div`
  .ant-pagination {
    display: flex !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }
`;

const FilterDropdownContainer = styled.div`
  padding: 8px;
`;

const FilterInput = styled(Input)`
  margin-bottom: 8px;
  display: block;
`;

const FilterButtonContainer = styled(Space)`
  /* Styled Space component for filter buttons */
`;

const FilterButton = styled(Button)`
  width: 90px;
`;

const TooltipContent = styled.span`
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CompanyTooltipContent = styled.span`
  display: block;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const GlobalSearchInput = styled(Input.Search)`
  width: 400px;
`;

const StyledTable = styled(Table<any>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: #003249;
    }
  }

  .ant-table-thead > tr > th {
    background-color: ${themeTokens.tableHeaderBg};
    color: ${themeTokens.textLight};
    font-weight: 600;
  }

  /* Don't have properties in theme config for filter and icons bg color */
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: #fff;
  }
  .ant-table-filter-trigger.active {
    color: #fff;
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: #fff;
  }

  .action-icon {
    cursor: pointer;
    color: black;
    margin-right: 8px;
  }

  .action-icon:hover {
    color: ${themeTokens.primaryColor};
  }
`;

export {
  Container,
  Header,
  HeaderLeft,
  BackButton,
  PageTitle,
  GlobalSearchContainer,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  TooltipContent,
  CompanyTooltipContent,
  GlobalSearchInput,
  StyledTable
};
