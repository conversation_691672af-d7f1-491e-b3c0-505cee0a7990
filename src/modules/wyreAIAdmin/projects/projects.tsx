import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Space, Tag, Tooltip, Select, Modal, Form, Popconfirm, notification } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectDTO } from 'src/api';
import { projectAPI, userAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';

import {
  Con<PERSON>er,
  Header,
  HeaderLeft,
  BackButton,
  PageTitle,
  GlobalSearchContainer,
  StyledTableContainer,
  FilterDropdownContainer,
  FilterInput,
  FilterButtonContainer,
  FilterButton,
  TooltipContent,
  CompanyTooltipContent,
  GlobalSearchInput,
  StyledTable
} from './projects.style';
import { queryKeys, appRoutes } from '../../utils/constant';
import { extractErrorMessage } from '../../utils/errorHandler';
import { formatDate } from '../../utils/util';

interface ExtendedProjectDTO extends ProjectDTO {
  companyName?: string;
  projectNumber?: string;
  projectType?: string;
  subscriptionType?: string;
  subscriptionStatus?: string;
  bidCaptainName?: string;
  numberOfVersions?: number;
}

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [globalSearchText, setGlobalSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [columnFilters, setColumnFilters] = useState<Record<string, any>>({});
  const [changeOwnerModalVisible, setChangeOwnerModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ExtendedProjectDTO | null>(null);

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(globalSearchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [globalSearchText]);

  // Fetch all projects with backend search and filtering
  const { data: projectsData, isLoading } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage,
      pageSize,
      debouncedSearchText,
      sortField,
      sortOrder,
      columnFilters
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        columnFilters.companyName || undefined, // companyId filter (will be updated when API supports it)
        debouncedSearchText || columnFilters.name || undefined, // name (global search + column filter)
        columnFilters.description || undefined, // description filter
        columnFilters.location || undefined, // location filter
        columnFilters.status || undefined, // status filter
        columnFilters.bidCaptainName || undefined, // owner filter (will be updated when API supports it)
        currentPage - 1, // page
        pageSize, // size
        [`${sortField},${sortOrder}`] // sort
      ),
    select: response => response.data
  });

  // Fetch users for bid captain selection
  const { data: usersData } = useQuery({
    queryKey: [queryKeys.usersList],
    queryFn: () => userAPI.getAllUsers(0, 1000),
    select: response => response.data
  });

  const handleBackClick = () => {
    navigate(`/${appRoutes.wyreAIAdmin}`);
  };

  const handleGlobalSearch = (value: string) => {
    setGlobalSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle sorting
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    }

    // Handle column filters
    if (filters) {
      const newFilters: Record<string, any> = {};
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          newFilters[key] = filters[key][0]; // Take first filter value
        }
      });
      setColumnFilters(newFilters);
      setCurrentPage(1); // Reset to first page when filtering
    }

    // Handle pagination
    if (pagination.current !== currentPage) {
      setCurrentPage(pagination.current);
    }

    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize);
      setCurrentPage(1);
    }
  };

  const handleChangeOwner = useCallback(
    (project: ExtendedProjectDTO) => {
      setSelectedProject(project);
      setChangeOwnerModalVisible(true);
      form.setFieldsValue({
        bidCaptain: project.bidCaptainId
      });
    },
    [form]
  );

  const handleChangeOwnerSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (selectedProject) {
        // API call to update project owner
        // await projectAPI.updateProject(selectedProject.id, { bidCaptainId: values.bidCaptain });
        setChangeOwnerModalVisible(false);
        setSelectedProject(null);
        form.resetFields();
      }
    } catch (error) {
      console.error('Failed to change owner:', error);
    }
  }, [form, selectedProject]);

  // Delete project mutation
  const { mutate: deleteProject } = useMutation({
    mutationFn: (projectId: number) => projectAPI.softDeleteProject(projectId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message: 'Project deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project deletion failed') });
    }
  });

  const handleDeleteProject = useCallback(
    (projectId: number) => {
      deleteProject(projectId);
    },
    [deleteProject]
  );

  // Filter functions for columns
  const getColumnSearchProps = useCallback(
    (dataIndex: string, placeholder: string) => ({
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <FilterDropdownContainer>
          <FilterInput
            placeholder={`Search ${placeholder}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />
          <FilterButtonContainer>
            <FilterButton
              type='primary'
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size='small'
            >
              Search
            </FilterButton>
            <FilterButton
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size='small'
            >
              Reset
            </FilterButton>
          </FilterButtonContainer>
        </FilterDropdownContainer>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const getColumnSelectProps = useCallback(
    (dataIndex: string, options: { text: string; value: string }[]) => ({
      filters: options,
      filterIcon: (filtered: boolean) => (
        <FilterOutlined
          style={{ color: filtered ? themeTokens.lightBlue : themeTokens.textLight }}
        />
      ),
      filteredValue: columnFilters[dataIndex] ? [columnFilters[dataIndex]] : null
    }),
    [columnFilters]
  );

  const columns: ColumnsType<ExtendedProjectDTO> = useMemo(
    () => [
      {
        title: 'Company Name',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('companyName', 'company name'),
        render: (companyName: string) => (
          <Tooltip title={companyName}>
            <CompanyTooltipContent>{companyName || '-'}</CompanyTooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Name',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: true,
        ...getColumnSearchProps('name', 'project name'),
        render: (name: string) => (
          <Tooltip title={name}>
            <TooltipContent>{name || '-'}</TooltipContent>
          </Tooltip>
        )
      },
      {
        title: 'Project Number',
        dataIndex: 'projectNumber',
        key: 'projectNumber',
        width: 120,
        render: (projectNumber: string) => projectNumber || '-'
      },
      {
        title: 'Project Type',
        dataIndex: 'projectType',
        key: 'projectType',
        width: 120,
        render: (projectType: string) => projectType || '-'
      },
      {
        title: 'Subscription',
        dataIndex: 'subscriptionType',
        key: 'subscriptionType',
        width: 120,
        render: (subscriptionType: string) => (
          <Tag
            color={
              subscriptionType === 'Enterprise' ? themeTokens.successGreen : themeTokens.infoBlue
            }
          >
            {subscriptionType || 'Non-Enterprise'}
          </Tag>
        )
      },
      {
        title: 'Subscription Status',
        dataIndex: 'subscriptionStatus',
        key: 'subscriptionStatus',
        width: 140,
        ...getColumnSelectProps('subscriptionStatus', [
          { text: 'Active', value: 'Active' },
          { text: 'Expired', value: 'Expired' },
          { text: 'Pending', value: 'Pending' }
        ]),
        render: (status: string) => {
          let color = themeTokens.warningOrange;
          if (status === 'Active') {
            color = themeTokens.successGreen;
          } else if (status === 'Expired') {
            color = themeTokens.dangerRed;
          }
          return <Tag color={color}>{status || 'Unknown'}</Tag>;
        }
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (createdAt: string) => formatDate(createdAt)
      },
      {
        title: 'Bid Captain',
        dataIndex: 'bidCaptainName',
        key: 'bidCaptainName',
        width: 150,
        sorter: true,
        ...getColumnSearchProps('bidCaptainName', 'bid captain'),
        render: (bidCaptainName: string) => bidCaptainName || '-'
      },
      {
        title: 'Bid Due Date',
        dataIndex: 'endDate',
        key: 'endDate',
        width: 120,
        sorter: true,
        render: (endDate: string) => (endDate ? formatDate(endDate) : '-')
      },
      {
        title: 'Versions',
        dataIndex: 'numberOfVersions',
        key: 'numberOfVersions',
        width: 80,
        render: (numberOfVersions: number) => numberOfVersions || 0
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, record) => (
          <Space size='small'>
            <EditOutlined
              className='action-icon'
              onClick={() => handleChangeOwner(record)}
              title='Change Owner'
            />
            <Popconfirm
              title='Delete Project'
              description='Are you sure you want to delete this project?'
              onConfirm={() => handleDeleteProject(record.projectId!)}
              okText='Yes, Delete'
              cancelText='Cancel'
              okType='danger'
            >
              <DeleteOutlined className='action-icon' title='Delete Project' />
            </Popconfirm>
          </Space>
        )
      }
    ],
    [getColumnSearchProps, getColumnSelectProps, handleChangeOwner, handleDeleteProject]
  );

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <BackButton icon={<ArrowLeftOutlined />} onClick={handleBackClick} type='text'>
            Back
          </BackButton>
          <PageTitle>Projects Management</PageTitle>
        </HeaderLeft>
      </Header>

      <GlobalSearchContainer>
        <GlobalSearchInput
          placeholder='Search across all projects...'
          allowClear
          enterButton={<SearchOutlined />}
          size='large'
          onSearch={handleGlobalSearch}
          onChange={e => {
            if (!e.target.value) {
              handleGlobalSearch('');
            }
          }}
        />
      </GlobalSearchContainer>

      <StyledTableContainer>
        <StyledTable
          columns={columns}
          dataSource={projectsData?.content || []}
          rowKey='projectId'
          loading={isLoading}
          onChange={handleTableChange}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: projectsData?.totalElements || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} projects`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1400 }}
        />
      </StyledTableContainer>

      <Modal
        title='Change Project Owner'
        open={changeOwnerModalVisible}
        onOk={handleChangeOwnerSubmit}
        onCancel={() => {
          setChangeOwnerModalVisible(false);
          setSelectedProject(null);
          form.resetFields();
        }}
        okText='Update Owner'
        cancelText='Cancel'
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            name='bidCaptain'
            label='New Bid Captain'
            rules={[{ required: true, message: 'Please select a bid captain' }]}
          >
            <Select
              placeholder='Select a bid captain'
              showSearch
              optionFilterProp='children'
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {usersData?.content?.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Container>
  );
};

export default Projects;
