import { useEffect } from 'react';
import { Navigate } from 'react-router';
import useGlobalStore from 'src/store/useGlobalStore';
import { appRoutes } from '../utils/constant';

const LoginPage: React.FC = () => {
  const { isAuthenticated } = useGlobalStore();

  useEffect(() => {
    // Redirect to Keycloak login if not authenticated
    if (!isAuthenticated) {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
      window.location.href = `${apiBaseUrl}/oauth2/authorization/keycloak`;
    }
  }, [isAuthenticated]);

  // If already authenticated, redirect to projects page
  if (isAuthenticated) {
    return <Navigate to={appRoutes.projects} />;
  }

  return null;
};

export { LoginPage };
