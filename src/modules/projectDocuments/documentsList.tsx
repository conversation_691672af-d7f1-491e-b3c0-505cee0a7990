import { FilterFilled } from '@ant-design/icons';
import {
  Inject,
  PdfViewerComponent,
  Toolbar,
  Magnification,
  Navigation,
  LinkAnnotation,
  BookmarkView,
  ThumbnailView,
  Print,
  TextSelection,
  TextSearch,
  Annotation,
  FormFields,
  FormDesigner
} from '@syncfusion/ej2-react-pdfviewer';
import { useQuery } from '@tanstack/react-query';
import { Button, DatePicker, Flex, Modal, Space, Table, Tooltip } from 'antd';
import { TableProps } from 'antd/es/table';
import { FilterDropdownProps, ColumnsType } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { MdOutlineFileUpload } from 'react-icons/md';
import { useParams } from 'react-router-dom';
import {
  DocumentWithPresignedUrlDTO,
  GetProjectDocumentsWithVersionsInputDocumentTypeEnum
} from 'src/api';
import { documentAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import UploadDocuments from './uploadDocuments';
import ColumnFilter from '../common/columnFilter';
import { DocProcessingJobStatus, queryKeys } from '../utils/constant';

interface DataType {
  title: string;
  inputDocumentType: GetProjectDocumentsWithVersionsInputDocumentTypeEnum;
  documentSetVersion: string;
  createdAt: string;
  createdByName: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const HeaderWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const StyledModal = styled(Modal)`
  & .ant-modal-content {
    left: 125px;
  }
`;
const FileNameEllipsis = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: '100%';
`;

const DocumentTypeWrapper = styled.span`
  text-transform: capitalize;
`;

const DateAndButtonsWrapper = styled(Space)`
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
`;

const DocumentName = styled.a`
  cursor: 'pointer';
`;

const StyledTable = styled(Table<DataType>)`
  .editable-row {
    transition: opacity 0.3s;
  }
  .edit-icon {
    cursor: pointer;
    color: black;
    size: 100px;
  }
  .editable-row:hover .edit-icon {
    display: inline-block;
    color: black;
    size: 100px;
  }
  /* Custom header hover style */
  && {
    .ant-table-thead > tr > th:hover {
      background-color: ${themeTokens.primaryDark};
    }
  }

  /*Don't have properties in theme config for filter and icons bg color /*
  /* White filter and sort icons */
  .ant-table-thead .anticon {
    color: ${themeTokens.textLight};
  }
  .ant-table-filter-trigger.active {
    color: ${themeTokens.textLight};
  }
  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down,
  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: ${themeTokens.textLight};
  }
`;

const StyledButton = styled(Button)`
  width: 90px;
`;

const DocumentsList = () => {
  const { projectId } = useParams();
  const [isViewDocumentClicked, setIsViewDocumentClicked] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [isUploadDocumentModalOpen, setIsUploadDocumentModalOpen] = useState(false);
  const [filters, setFilters] = useState<{
    title?: string[];
    createdByName?: string[];
    createdAt?: number[];
    inputDocumentType?: GetProjectDocumentsWithVersionsInputDocumentTypeEnum[];
    documentSetVersion?: string[];
  }>({});
  const [sortedInfo, setSortedInfo] = useState<[string, 'asc' | 'desc'] | undefined>(undefined);
  const { RangePicker } = DatePicker;
  const [datePickerKey, setDatePickerKey] = useState(0);
  const { documentProcessingListeners } = useGlobalStore();
  const documentProcessingStatus = useMemo(() => {
    if (documentProcessingListeners && projectId) {
      return documentProcessingListeners[Number(projectId)];
    }
    return null;
  }, [documentProcessingListeners, projectId]);

  const { data: documentsList } = useQuery({
    queryKey: [queryKeys.documentsList, projectId, filters, sortedInfo],
    queryFn: () =>
      documentAPI.getProjectDocumentsWithVersions(
        Number(projectId),
        filters.documentSetVersion,
        filters.title,
        filters.createdByName,
        filters.createdAt,
        filters.inputDocumentType,
        undefined,
        1000,
        sortedInfo
      ),
    select: res => res.data,
    enabled: !!projectId
  });

  const { data: documentsFilters } = useQuery({
    queryKey: [queryKeys.documentsFilters, projectId],
    queryFn: () =>
      documentAPI.getProjectDocumentsWithVersions(
        Number(projectId),
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        1000
      ),
    select: res => res.data,
    enabled: !!projectId
  });

  const handleTitleClick = (record: DocumentWithPresignedUrlDTO) => {
    setIsViewDocumentClicked(true);
    if (record.presignedUrl) setPdfUrl(record.presignedUrl);
  };

  const columns: ColumnsType<DataType> = useMemo(
    () => [
      {
        title: 'Document Name',
        dataIndex: 'title',
        key: 'title',
        render: (text: string, record: DocumentWithPresignedUrlDTO) => (
          <FileNameEllipsis>
            <Tooltip title={text}>
              <DocumentName onClick={() => handleTitleClick(record)}>{text}</DocumentName>
            </Tooltip>
          </FileNameEllipsis>
        ),
        ...ColumnFilter(
          'title',
          Array.from(new Set(documentsFilters?.content?.map(item => item.title))) as string[]
        ),
        sorter: true
      },
      {
        title: 'Document Type',
        dataIndex: 'inputDocumentType',
        key: 'inputDocumentType',
        width: '13%',
        render: (text: string) => (
          <DocumentTypeWrapper>{`${text?.toLowerCase()}s`}</DocumentTypeWrapper>
        ),
        ...ColumnFilter(
          'inputDocumentType',
          Array.from(
            new Set(documentsFilters?.content?.map(item => item.inputDocumentType))
          ) as string[]
        ),
        sorter: true
      },
      {
        title: 'Version',
        dataIndex: 'documentSetVersion',
        key: 'documentSetVersion',
        ...ColumnFilter(
          'documentSetVersion',
          Array.from(
            new Set(documentsFilters?.content?.map(item => item.documentSetVersion))
          ) as string[]
        ),
        sorter: true
      },
      {
        title: 'Created Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: '13%',
        render: (text: string) => {
          const timestamp = parseFloat(text) * 1000;
          return dayjs(timestamp).format('MM/DD/YYYY');
        },
        filterDropdown: ({ setSelectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
          <DateAndButtonsWrapper>
            <RangePicker
              key={datePickerKey}
              onChange={dates => {
                if (dates && dates[0] && dates[1]) {
                  const startOfDay = dates[0].startOf('day').unix();
                  const endOfDay = dates[1].endOf('day').unix();
                  setSelectedKeys([`${startOfDay},${endOfDay}`]);
                } else {
                  setSelectedKeys([]);
                }
              }}
            />
            <Space>
              <StyledButton type='primary' size='small' onClick={() => confirm()}>
                Search
              </StyledButton>
              <StyledButton
                size='small'
                onClick={() => {
                  clearFilters?.();
                  confirm();
                  setDatePickerKey(prev => prev + 1);
                }}
              >
                Reset
              </StyledButton>
            </Space>
          </DateAndButtonsWrapper>
        ),
        filterIcon: (filtered: boolean) => (
          <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
        ),
        sorter: true
      },
      {
        title: 'Created By',
        dataIndex: 'createdByName',
        key: 'createdByName',
        ...ColumnFilter(
          'createdByName',
          Array.from(
            new Set(documentsFilters?.content?.map(item => item.createdByName))
          ) as string[]
        ),
        sorter: true
      }
    ],
    [documentsList, documentsFilters]
  );

  const handleTableChange: TableProps<DataType>['onChange'] = (pagination, filters, sorter) => {
    setFilters(filters);
    const primarySorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const sortInfo: [string, 'asc' | 'desc'] | undefined =
      primarySorter?.field && primarySorter.order
        ? [String(primarySorter.field), primarySorter.order === 'ascend' ? 'asc' : 'desc']
        : undefined;
    setSortedInfo(sortInfo);
  };

  return (
    <Container>
      <HeaderWrapper>
        <Header>Documents</Header>
        <Flex gap={15}>
          {documentProcessingStatus === DocProcessingJobStatus.inProgress ? (
            <Tooltip title='Documents are currently being processed'>
              <Button type='primary' disabled={true}>
                <MdOutlineFileUpload size={22} />
                Upload Documents
              </Button>
            </Tooltip>
          ) : (
            <Button type='primary' onClick={() => setIsUploadDocumentModalOpen(true)}>
              <MdOutlineFileUpload size={22} />
              Upload Documents
            </Button>
          )}
          {isUploadDocumentModalOpen && (
            <UploadDocuments
              documentsCount={documentsList?.content?.length || 0}
              isDocumentsScreen={true}
              isUploadDocumentModalOpen={isUploadDocumentModalOpen}
              setIsUploadDocumentModalOpen={setIsUploadDocumentModalOpen}
            />
          )}
        </Flex>
      </HeaderWrapper>
      <StyledTable
        bordered
        dataSource={(documentsList?.content as DataType[]) || []}
        columns={columns}
        pagination={false}
        tableLayout='fixed'
        onChange={handleTableChange}
      />
      <StyledModal
        width='75vw'
        open={isViewDocumentClicked}
        footer={null}
        onCancel={() => setIsViewDocumentClicked(false)}
        title='Document Viewer'
        centered
      >
        <PdfViewerComponent
          height={'calc(100vh - 250px)'}
          documentPath={pdfUrl}
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          enableHyperlink={true}
          enablePersistence={true}
          zoomValue={50}
        >
          <Inject
            services={[
              Toolbar,
              Magnification,
              Navigation,
              Annotation,
              LinkAnnotation,
              BookmarkView,
              ThumbnailView,
              Print,
              TextSelection,
              TextSearch,
              FormFields,
              FormDesigner
            ]}
          />
        </PdfViewerComponent>
      </StyledModal>
    </Container>
  );
};

export default DocumentsList;
