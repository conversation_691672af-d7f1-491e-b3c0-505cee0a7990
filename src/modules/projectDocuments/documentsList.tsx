import {
  Inject,
  PdfViewerComponent,
  Toolbar,
  Magnification
} from '@syncfusion/ej2-react-pdfviewer';
import { useQuery } from '@tanstack/react-query';
import { Flex, Modal, Table } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { DocumentDTOInputDocumentTypeEnum, DocumentWithPresignedUrlDTO } from 'src/api';
import { documentAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import UploadDocuments from './uploadDocuments';
import { queryKeys } from '../utils/constant';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const HeaderWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const StyledModal = styled(Modal)`
  & .ant-modal-content {
    left: 125px;
  }
`;
const FileNameEllipsis = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: '100%';
`;

const DocumentName = styled.a`
  cursor: 'pointer';
`;

const DocumentsList = () => {
  const { projectId } = useParams();
  const { selectedVersion } = useGlobalStore();
  const [isViewDocumentClicked, setIsViewDocumentClicked] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [isUploadDocumentModalOpen, setIsUploadDocumentModalOpen] = useState(false);

  const { data: documentsList } = useQuery({
    queryKey: [queryKeys.documentsList, projectId, selectedVersion],
    queryFn: () =>
      documentAPI.getDocumentsByProjectAndVersion(
        Number(projectId),
        String(selectedVersion?.version || '')
      ),
    select: res => res.data,
    enabled: !!projectId && !!selectedVersion
  });

  const handleTitleClick = (record: DocumentWithPresignedUrlDTO) => {
    setIsViewDocumentClicked(true);
    if (record.presignedUrl) setPdfUrl(record.presignedUrl);
  };

  const columns = useMemo(
    () => [
      {
        title: 'Document Name',
        dataIndex: 'title',
        key: 'title',
        render: (text: string, record: DocumentWithPresignedUrlDTO) => (
          <FileNameEllipsis>
            <DocumentName onClick={() => handleTitleClick(record)}>{text}</DocumentName>
          </FileNameEllipsis>
        )
      },
      {
        title: 'Created By',
        dataIndex: 'createdByName',
        key: 'createdByName',
        width: '30%'
      },
      {
        title: 'Date',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: '20%',
        render: (text: string) => {
          const timestamp = parseFloat(text) * 1000;
          return dayjs(timestamp).format('MM/DD/YYYY');
        }
      }
    ],
    []
  );

  return (
    <Container>
      <HeaderWrapper>
        <Header>Drawings</Header>
        <Flex gap={15}>
          <UploadDocuments
            documentsCount={documentsList?.content?.length || 0}
            isDocumentsScreen={true}
            isUploadDocumentModalOpen={isUploadDocumentModalOpen}
            setIsUploadDocumentModalOpen={setIsUploadDocumentModalOpen}
          />
        </Flex>
      </HeaderWrapper>
      <Table
        bordered
        dataSource={
          documentsList?.content?.filter(
            item => item.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Drawing
          ) || []
        }
        columns={columns}
        pagination={false}
        tableLayout='fixed'
      />
      <Header>Specifications</Header>
      <Table
        bordered
        dataSource={
          documentsList?.content?.filter(
            item => item.inputDocumentType === DocumentDTOInputDocumentTypeEnum.Specification
          ) || []
        }
        columns={columns}
        pagination={false}
        tableLayout='fixed'
      />
      <StyledModal
        width='75vw'
        open={isViewDocumentClicked}
        footer={null}
        onCancel={() => setIsViewDocumentClicked(false)}
        title='Document Viewer'
        centered
      >
        <PdfViewerComponent
          height={'calc(100vh - 250px)'}
          documentPath={pdfUrl}
          resourceUrl={`${window.location.origin}/ej2-pdfviewer-lib`}
          toolbarSettings={{ showTooltip: true, toolbarItems: ['MagnificationTool'] }}
          enableHyperlink={true}
          enablePersistence={true}
          zoomValue={50}
        >
          <Inject services={[Toolbar, Magnification]} />
        </PdfViewerComponent>
      </StyledModal>
    </Container>
  );
};

export default DocumentsList;
