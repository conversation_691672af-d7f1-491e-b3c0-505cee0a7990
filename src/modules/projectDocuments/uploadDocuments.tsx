import { useMutation, useQueryClient } from '@tanstack/react-query';
import { App, Button, Flex, Form, Input, Modal, Spin, UploadFile } from 'antd';
import { UploadChangeParam } from 'antd/es/upload';
import { AxiosResponse } from 'axios';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { FaCircleCheck } from 'react-icons/fa6';
import { RxCrossCircled } from 'react-icons/rx';
import { useParams } from 'react-router-dom';
import {
  DocumentUploadConfirmDTO,
  DocumentUploadConfirmDTOInputDocumentTypeEnum,
  PresignedUrlResponse
} from 'src/api';
import { documentAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CustomIcon from '../common/customIcon';
import fileUploader from '../projects/components/fileUploader';
import { FileFormatSupprotType } from '../projects/createProject';
import {
  analysingDocumentsStatusText,
  APIMutationStatus,
  buttonText,
  DocProcessingJobStatus,
  fileEnums,
  queryKeys
} from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';

interface UploadDocumentsProps {
  isUploadDocumentModalOpen: boolean;
  setIsUploadDocumentModalOpen: (value: boolean) => void;
  isDocumentsScreen: boolean;
  documentsCount?: number;
}

const StyledButton = styled(Button)`
  border: 1px solid ${themeTokens.buttonBorder};
`;

const StyledSaveButton = styled(Button)`
  background-color: ${themeTokens.buttonDark};
`;

const LoaderContainer = styled.div`
  width: 100%;
  height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
`;

const LoadingInfoTitle = styled.p<{ isTitleAvailable?: boolean }>`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: ${({ isTitleAvailable }) => (isTitleAvailable ? 500 : 400)};
  font-size: ${({ isTitleAvailable }) => (isTitleAvailable ? 26 : 20)}px;
  line-height: 25px;
  text-align: center;
`;

const LoadingInfoDescription = styled.p`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
`;

const StyledLabel = styled.div`
  font-family: inter;
  color: ${themeTokens.textGray};
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
`;

const UploadDocuments: FC<UploadDocumentsProps> = ({
  isUploadDocumentModalOpen,
  setIsUploadDocumentModalOpen,
  isDocumentsScreen,
  documentsCount
}) => {
  const { projectId } = useParams();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [drawingsFile, setDrawingsFile] = useState<Array<UploadFile>>([]);
  const [specificationsFile, setSpecificationsFile] = useState<Array<UploadFile>>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const { selectedProjectName } = useGlobalStore();
  const { notification } = App.useApp();
  const [documentsUploadStatus, setDocumentsUploadStatus] = useState<APIMutationStatus>(
    APIMutationStatus.idle
  );
  const { selectedVersion, setSelectedVersion } = useGlobalStore();
  const [fileFormatNotSupported, setFileFormatNotSupported] = useState<FileFormatSupprotType>({
    drawings: false,
    specifications: false
  });

  const hasAnyDocuments = useMemo(
    () => drawingsFile.length > 0 || specificationsFile.length > 0,
    [drawingsFile, specificationsFile]
  );

  const documents = useMemo(
    () => [...drawingsFile, ...specificationsFile],
    [drawingsFile, specificationsFile]
  );

  const fileUploadList = [
    {
      title: fileEnums.uploadDrawings,
      file: drawingsFile,
      isFilesFormatSupported: fileFormatNotSupported.drawings
    },
    {
      title: fileEnums.uploadSpecifications,
      file: specificationsFile,
      isFilesFormatSupported: fileFormatNotSupported.specifications
    }
  ];

  const {
    mutate: confirmDocumentsUpload,
    status: confirmDocumentsUploadStatus,
    reset: resetConfirmDocumentsUpdate
  } = useMutation({
    mutationFn: (args: {
      projectId: number;
      documentSetVersionId: number;
      documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>;
    }) =>
      documentAPI.confirmDocumentUpload1(
        args.projectId,
        args.documentSetVersionId,
        args.documentUploadConfirmDTO
      )
  });

  const buttonTextInfo = useMemo(() => {
    if (documentsUploadStatus === APIMutationStatus.idle) {
      return buttonText.upload;
    }
    if (documentsUploadStatus === APIMutationStatus.pending) {
      return buttonText.continue;
    }
    if (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    ) {
      return buttonText.continue;
    }
    return buttonText.continue;
  }, [documentsUploadStatus, confirmDocumentsUploadStatus]);

  const isTitleAvailable = useMemo(() => {
    return (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    );
  }, [confirmDocumentsUploadStatus, documentsUploadStatus]);

  const analysingDocumentsStatusInfo = useMemo(() => {
    if (documentsUploadStatus === APIMutationStatus.pending)
      return {
        title: analysingDocumentsStatusText.uploadingDocuments,
        icon: <Spin size='large' />
      };

    if (
      confirmDocumentsUploadStatus === APIMutationStatus.error ||
      documentsUploadStatus === APIMutationStatus.error
    )
      return {
        title: analysingDocumentsStatusText.somethingWentWrong,
        icon: <CustomIcon Icon={RxCrossCircled} size='70px' />
      };
    if (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    )
      return {
        title: (
          <>
            {analysingDocumentsStatusText.ScopeBuilderAnalyzingDocuments} <br />
            {analysingDocumentsStatusText.ScopeBuilderAnalyzingDocumentsSubText}
          </>
        ),
        icon: <CustomIcon Icon={FaCircleCheck} size='70px' />
      };
    return { title: analysingDocumentsStatusText.documentsAnalysed, icon: <Spin size='large' /> };
  }, [documentsUploadStatus, confirmDocumentsUploadStatus]);

  const findDocument = (fileId: string) => {
    const drawing = drawingsFile.find(file => file.uid === fileId);
    const specification = specificationsFile.find(file => file.uid === fileId);
    if (specification) return { file: specification, isDrawing: false };
    return { file: drawing, isDrawing: true };
  };

  useEffect(() => {
    setFileFormatNotSupported({
      drawings: !drawingsFile.every(item => item.type === 'application/pdf'),
      specifications: !specificationsFile.every(item => item.type === 'application/pdf')
    });
  }, [drawingsFile, specificationsFile]);

  const convertRcFileToFile = (rcFile: UploadFile): File | undefined => {
    const { name, lastModified, type, originFileObj } = rcFile;
    if (originFileObj)
      return new File([originFileObj], name, {
        lastModified,
        type
      });
    return undefined;
  };

  const uploadFileToS3 = async (file: File, presignedUrl: string): Promise<void> => {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to upload file: ${response.statusText}`);
    }
  };

  const onProjectCreated = async (response: AxiosResponse<PresignedUrlResponse[], any>) => {
    if (hasAnyDocuments) {
      setDocumentsUploadStatus(APIMutationStatus.pending);
      // TODO :  need to get from API response
      const documentSetVersionId = response?.data[0]?.documentSetVersionId;
      const presignedUrls = response?.data;

      if (presignedUrls) {
        const fileUploadPromises = presignedUrls.map(async (item: PresignedUrlResponse, index) => {
          if (item.fileId && item.url && item.fileKey) {
            const documentInfo = findDocument(item.fileId);
            if (documentInfo.file) {
              const file = convertRcFileToFile(documentInfo.file);
              if (file) {
                await uploadFileToS3(file, item.url);
                const documentUploadConfirmDTO: DocumentUploadConfirmDTO = {
                  fileKey: item.fileKey,
                  size: file.size,
                  mimeType: file.type,
                  filePath: file.name,
                  inputDocumentType: documentInfo.isDrawing
                    ? DocumentUploadConfirmDTOInputDocumentTypeEnum.Drawing
                    : DocumentUploadConfirmDTOInputDocumentTypeEnum.Specification,
                  documentId: item.documentId
                };
                return documentUploadConfirmDTO;
              }
            }
          }
        });
        try {
          const result = await Promise.all(fileUploadPromises);
          setDocumentsUploadStatus(APIMutationStatus.success);
          const filteredResults = result.filter(item => item !== undefined);
          const results = filteredResults as Array<DocumentUploadConfirmDTO>;
          if (results.length > 0 && Number(projectId) && documentSetVersionId) {
            confirmDocumentsUpload({
              projectId: Number(projectId),
              documentSetVersionId,
              documentUploadConfirmDTO: results
            });
          }
        } catch (error) {
          setDocumentsUploadStatus(APIMutationStatus.error);
          console.error('Error uploading file:', error);
        }
      }
    }
  };

  const { mutateAsync: uploadFiles, reset } = useMutation({
    mutationFn: () =>
      documentAPI.generateUrlsToUpload(
        Number(projectId),
        documents.map(document => ({ docId: document.uid, docName: document.name })),
        documentsCount ? undefined : Number(selectedVersion?.versionId),
        documentsCount ? form.getFieldsValue().documentVersion : undefined
      ),
    onSuccess: onProjectCreated,
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Uploading documents failed') });
      reset();
    }
  });

  useEffect(() => {
    if (!documentsCount) {
      form.setFieldValue('documentVersion', selectedVersion?.version);
    }
  }, [documentsCount, selectedVersion?.version]);

  const resetModalState = () => {
    setDrawingsFile([]);
    setSpecificationsFile([]);
    resetConfirmDocumentsUpdate();
    setDocumentsUploadStatus(APIMutationStatus.idle);
  };

  const cancelHandler = useCallback(() => {
    resetModalState();
    setIsUploadDocumentModalOpen(false);
    form.resetFields();
  }, [form]);

  const saveHandler = async () => {
    await form.validateFields();
    if (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    ) {
      cancelHandler();
      if (!isDocumentsScreen) {
        queryClient.invalidateQueries({
          queryKey: [queryKeys.allScopes]
        });
      } else {
        if (documentsCount) setSelectedVersion(null);
        queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
        queryClient.invalidateQueries({ queryKey: [queryKeys.documentsList] });
        queryClient.invalidateQueries({ queryKey: [queryKeys.versionList] });
        queryClient.invalidateQueries({ queryKey: [queryKeys.documentsFilters] });
      }
    }

    if (hasAnyDocuments && documentsUploadStatus === APIMutationStatus.idle) {
      uploadFiles();
    }
    if (
      documentsUploadStatus === APIMutationStatus.error ||
      confirmDocumentsUploadStatus === APIMutationStatus.error
    ) {
      cancelHandler();
    }
  };

  const handleClose = (e: React.MouseEvent, name: string, index: number) => {
    e.stopPropagation();
    if (name === fileEnums.uploadDrawings) {
      const drawings = [...drawingsFile];
      drawings.splice(index, 1);
      setDrawingsFile(drawings);
    } else if (name === fileEnums.uploadSpecifications) {
      const specifications = [...specificationsFile];
      specifications.splice(index, 1);
      setSpecificationsFile(specifications);
    }
  };

  const handleOnChange = (info: UploadChangeParam<UploadFile<any>>, name: string) => {
    setUploading(true);
    if (name === fileEnums.uploadDrawings) setDrawingsFile(info.fileList);
    else setSpecificationsFile(info.fileList);
    setUploading(false);
  };

  return (
    <>
      <Modal
        title={selectedProjectName}
        width='40%'
        open={isUploadDocumentModalOpen}
        centered
        maskClosable={false}
        closeIcon={null}
        footer={
          <Flex justify='end' gap={8}>
            <StyledButton
              onClick={cancelHandler}
              disabled={
                documentsUploadStatus === APIMutationStatus.pending ||
                documentsUploadStatus === APIMutationStatus.success
              }
            >
              Cancel
            </StyledButton>
            <StyledSaveButton
              type='primary'
              onClick={saveHandler}
              loading={documentsUploadStatus === APIMutationStatus.pending}
              disabled={
                (documentsUploadStatus === APIMutationStatus.idle && !hasAnyDocuments) ||
                documentsUploadStatus === APIMutationStatus.pending ||
                confirmDocumentsUploadStatus === APIMutationStatus.pending ||
                fileFormatNotSupported.drawings ||
                fileFormatNotSupported.specifications
              }
            >
              {buttonTextInfo}
            </StyledSaveButton>
          </Flex>
        }
      >
        <>
          {hasAnyDocuments && documentsUploadStatus !== APIMutationStatus.idle ? (
            <LoaderContainer>
              {isTitleAvailable && (
                <LoadingInfoTitle isTitleAvailable={isTitleAvailable}>
                  We’re on it!
                </LoadingInfoTitle>
              )}
              <LoadingInfoTitle isTitleAvailable={!isTitleAvailable}>
                {analysingDocumentsStatusInfo.title}
              </LoadingInfoTitle>
              {analysingDocumentsStatusInfo.icon}
              {(documentsUploadStatus === APIMutationStatus.pending ||
                confirmDocumentsUploadStatus === APIMutationStatus.pending) && (
                <LoadingInfoDescription>Please wait</LoadingInfoDescription>
              )}
            </LoaderContainer>
          ) : (
            <Flex vertical gap={32}>
              <Flex vertical gap={8}>
                <Form form={form} layout='vertical'>
                  <Form.Item
                    label='Document Version'
                    name='documentVersion'
                    rules={[
                      {
                        required: true,
                        message:
                          'Document upload requires a version name. Please enter it to proceed'
                      }
                    ]}
                  >
                    <Input placeholder='Enter document version' disabled={!documentsCount} />
                  </Form.Item>
                </Form>
              </Flex>
              <Flex vertical gap={32}>
                {fileUploadList.map(({ title, file, isFilesFormatSupported }) =>
                  fileUploader(
                    title,
                    uploading,
                    file,
                    handleClose,
                    handleOnChange,
                    isFilesFormatSupported
                  )
                )}
              </Flex>
            </Flex>
          )}
        </>
      </Modal>
    </>
  );
};

export default UploadDocuments;
