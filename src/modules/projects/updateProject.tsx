import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Modal, Form, App, Input } from 'antd';
import { isEqual } from 'lodash';
import React, { useEffect, useCallback, useMemo } from 'react';
import { ProjectDTO, ProjectUpdateDTO } from 'src/api';
import { projectAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { APIMutationStatus, queryKeys } from '../utils/constant';

interface UpdateProjectModalProps {
  visible: boolean;
  onClose: () => void;
  projectData?: ProjectDTO;
}

const UpdateProjectModal: React.FC<UpdateProjectModalProps> = ({
  visible,
  onClose,
  projectData
}) => {
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const queryClient = useQueryClient();
  const { selectedProjectId } = useGlobalStore();
  const { notification } = App.useApp();

  // Memoize the initial form values for edit mode
  const initialValues = useMemo(() => {
    if (projectData) {
      return {
        name: projectData.name,
        address: projectData.location,
        description: projectData.description,
        owner: projectData.owner
      };
    }
    return undefined;
  }, [projectData]);

  // Set form values when in edit mode
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const handleMutationSuccess = useCallback(
    (message: string) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.projectInfo] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message });
    },
    [queryClient, notification]
  );

  // Update project mutation
  const {
    mutate: updateProjectMutate,
    status: updateStatus,
    reset: updateReset
  } = useMutation({
    mutationFn: (data: { id: number; updateData: ProjectUpdateDTO }) =>
      projectAPI.updateProject(data.id, data.updateData),
    onSuccess: () => handleMutationSuccess('Project updated successfully'),
    onError: error =>
      notification.error({
        message: 'Project update failed',
        description: String(error)
      })
  });

  const hasFormChanged = useMemo(() => {
    if (!formValues) return;
    return !isEqual(formValues, initialValues);
  }, [formValues, initialValues]);

  const handleModalClose = useCallback(() => {
    form.resetFields();
    updateReset();
    onClose();
  }, [form, updateReset, onClose]);

  const updateProject = useCallback(async () => {
    try {
      const data: ProjectUpdateDTO = {
        projectId: Number(selectedProjectId),
        name: formValues?.name,
        location: formValues.address,
        description: formValues.description,
        ownerByName: formValues.owner
      };

      if (projectData?.projectId) {
        updateProjectMutate({ id: projectData.projectId, updateData: data });
      }
      handleModalClose();
    } catch (error) {
      notification.error({
        message: 'Project update failed',
        description: String(error)
      });
    }
  }, [
    selectedProjectId,
    formValues,
    projectData,
    handleModalClose,
    updateProjectMutate,
    notification
  ]);

  const handleSave = useCallback(async () => {
    if (updateStatus === APIMutationStatus.success) {
      handleModalClose();
      return;
    }
    try {
      await form.validateFields();
      await updateProject();
    } catch (error) {
      notification.error({
        message: 'Validation failed',
        description: String(error)
      });
    }
  }, [updateStatus, handleModalClose, form, updateProject, notification]);

  return (
    <Modal
      title={'Edit project'}
      open={visible}
      closeIcon={null}
      maskClosable={false}
      onCancel={onClose}
      okText='Save'
      centered
      onOk={handleSave}
      okButtonProps={{ disabled: !hasFormChanged }}
    >
      <Form form={form} layout='vertical'>
        <Form.Item
          label='Project Name'
          name='name'
          rules={[{ required: true, message: 'Please enter project name' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label='Address'
          name='address'
          rules={[{ required: true, message: 'Please enter project location' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label='Owner'
          name='owner'
          rules={[{ required: true, message: 'Please enter owner' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item label='Description' name='description'>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateProjectModal;
