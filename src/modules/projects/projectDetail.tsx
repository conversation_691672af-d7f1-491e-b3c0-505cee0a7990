import { QuestionCircleOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import { App, <PERSON><PERSON>, Card, Col, Divider, Flex, Popconfirm, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';
import { AiFillProject } from 'react-icons/ai';
import { BsFileEarmarkText } from 'react-icons/bs';
import { FaMapMarkerAlt, FaCalendarAlt, FaUser, FaBuilding } from 'react-icons/fa';
import { IoDocumentText } from 'react-icons/io5';
import { MdDelete, MdEdit } from 'react-icons/md';
import { useNavigate, useParams } from 'react-router-dom';
import {
  DocumentUploadConfirmDTOInputDocumentTypeEnum,
  DocumentWithPresignedUrlDTO
} from 'src/api';
import { documentAPI, projectAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import styled from 'styled-components';
import ProjectUserTable from './projectUserTable';
import UpdateProject from './updateProject';
import HasProjectPermission from '../guards/HasProjectPermission';
import { appRoutes, queryKeys } from '../utils/constant';
import { UserProjectPermission } from '../utils/permissions';

const ContainerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 45px;
  padding: 48px;
  width: 100%;
  height: 100%;
  position: relative;
`;

const Container = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

const Header = styled.div`
  font-weight: 500;
  font-size: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
`;

const ProjectCard = styled(Card)`
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  .ant-card-body {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
`;

const InfoRow = styled(Row)`
  display: flex;
  flex-wrap: wrap;
`;

const InfoItem = styled(Col)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DocumentInfoItem = styled(Col)`
  display: flex;
  align-items: flex-start;
  gap: 8px;
`;

const InfoIcon = styled.div`
  font-size: 16px;
  color: #5f6368;
`;

const InfoContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const InfoLabel = styled.div`
  font-weight: 600;
  font-size: 16px;
`;

const InfoValue = styled.div`
  font-weight: 400;
  font-size: 16px;
  color: #4e4e4e;
`;

const FileList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-top: 4px;
`;

const FileItem = styled.div`
  font-size: 14px;
`;

const ContentWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32px;
`;

interface DocumentInfoWithVersion {
  versionName: string;
  drawings: DocumentWithPresignedUrlDTO[];
  specifications: DocumentWithPresignedUrlDTO[];
}

interface DocumentItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  files: { id: string; name: string }[];
}

interface ProjectInfoItem {
  id: string;
  label: string;
  value: string | React.ReactNode;
  icon: React.ReactNode;
  colSpan?: number;
}

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { notification } = App.useApp();
  const [isUpdateProjectModalOpen, setIsUpdateProjectModalOpen] = useState<boolean>(false);
  const { selectedVersion } = useGlobalStore();

  const { data } = useQuery({
    queryKey: [queryKeys.projectInfo, projectId],
    queryFn: () => projectAPI.getProjectById(Number(projectId)),
    enabled: !!projectId,
    select: res => res.data
  });
  const { data: documentsData } = useQuery({
    queryKey: [queryKeys.projectDocumentsInfo, projectId, selectedVersion?.version],
    queryFn: () =>
      documentAPI.getDocumentsByProjectAndVersion(
        Number(projectId),
        String(selectedVersion?.version || '')
      ),
    select: res => res.data,
    enabled: !!projectId && !!selectedVersion
  });

  const { mutate, isPending: isDeletePending } = useMutation({
    mutationFn: () => projectAPI.softDeleteProject(Number(projectId)),
    onSuccess: () => {
      notification.success({ message: 'Project deleted successfully' });
      navigate(`/${appRoutes.projects}`);
    }
  });

  const onUpdateProjectModalClose = useCallback(() => setIsUpdateProjectModalOpen(false), []);

  const projectInfoItems: ProjectInfoItem[] = useMemo(
    () => [
      {
        id: 'address',
        label: 'Address:',
        value: data?.location || '-',
        icon: <FaMapMarkerAlt />
      },
      {
        id: 'bidDueDate',
        label: 'Bid Due Date:',
        value: dayjs(data?.endDate).format('MM/DD/YYYY') || '-',
        icon: <FaCalendarAlt />
      },
      {
        id: 'owner',
        label: 'Owner:',
        value: data?.owner || '-',
        icon: <FaUser />
      },
      {
        id: 'scopes',
        label: 'Scopes:',
        value: data?.scopeCount || '0',
        icon: <AiFillProject />
      },
      {
        id: 'projectType',
        label: 'Project Type:',
        value: 'Residential',
        icon: <FaBuilding />
      }
    ],
    [data]
  );

  const documentItems = useMemo(
    () => [
      {
        id: 'drawings',
        label: 'Drawings:',
        icon: <IoDocumentText />,
        files: documentsData?.content?.filter(
          doc => doc.inputDocumentType === DocumentUploadConfirmDTOInputDocumentTypeEnum.Drawing
        )
      },
      {
        id: 'specifications',
        label: 'Specification:',
        icon: <BsFileEarmarkText />,
        files: documentsData?.content?.filter(
          doc =>
            doc.inputDocumentType === DocumentUploadConfirmDTOInputDocumentTypeEnum.Specification
        )
      }
    ],
    [documentsData]
  );

  return (
    <ContainerWrapper>
      <Container>
        <Header>
          {data?.name}
          <MdEdit size={24} cursor='pointer' onClick={() => setIsUpdateProjectModalOpen(true)} />
        </Header>
        <ContentWrapper>
          <ProjectCard>
            <InfoRow gutter={[24, 24]}>
              {projectInfoItems.map(item => (
                <InfoItem key={item.id} xs={24} sm={item.colSpan || 8}>
                  <InfoIcon>{item.icon}</InfoIcon>
                  <InfoLabel>{item.label}</InfoLabel>
                  <InfoValue>{item.value}</InfoValue>
                </InfoItem>
              ))}
            </InfoRow>

            {documentItems.length > 0 &&
              documentItems.map((docItem, index) => {
                return (
                  <React.Fragment key={`version-${index}`}>
                    {docItem.files && docItem.files.length > 0 ? (
                      <InfoRow gutter={[24, 24]}>
                        <DocumentInfoItem key={docItem.id} xs={24} sm={12}>
                          <InfoIcon>{docItem.icon}</InfoIcon>
                          <InfoContent>
                            <InfoLabel>{docItem.label}</InfoLabel>
                            <FileList>
                              {docItem.files &&
                                docItem.files.map(file => (
                                  <FileItem key={file.id}>{file.filePath}</FileItem>
                                ))}
                            </FileList>
                          </InfoContent>
                        </DocumentInfoItem>
                      </InfoRow>
                    ) : null}
                  </React.Fragment>
                );
              })}
          </ProjectCard>
          <Flex justify='end'>
            <HasProjectPermission requiredPermissions={[UserProjectPermission.DELETE_PROJECT]}>
              <Popconfirm
                title='Delete project'
                description='Are you sure to delete this project?'
                onConfirm={() => mutate()}
                icon={<QuestionCircleOutlined />}
                onCancel={() => null}
                okText='Yes'
                cancelText='No'
              >
                <Button
                  danger
                  loading={isDeletePending}
                  disabled={isDeletePending}
                  icon={<MdDelete size={20} />}
                >
                  Delete Project
                </Button>
              </Popconfirm>
            </HasProjectPermission>
          </Flex>
          <Divider />
          <ProjectUserTable />
        </ContentWrapper>
      </Container>
      {isUpdateProjectModalOpen && (
        <UpdateProject
          visible={isUpdateProjectModalOpen}
          onClose={onUpdateProjectModalClose}
          projectData={data}
        />
      )}
    </ContainerWrapper>
  );
};

export default ProjectDetail;
