import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button, DatePicker, Flex, Form, Input, Modal, Spin, App, Select, Row, Col } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import { AxiosResponse } from 'axios';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { FaCircleCheck } from 'react-icons/fa6';
import { RxCrossCircled } from 'react-icons/rx';
import { useNavigate } from 'react-router';
import {
  DocumentUploadConfirmDTO,
  DocumentUploadConfirmDTOInputDocumentTypeEnum,
  PresignedUrlResponse,
  ProjectCreateDTO,
  ProjectWithPresignedUrlsDTO
} from 'src/api';
import { projectAPI, userAPI } from 'src/api/apiClient';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CustomIcon from '../common/customIcon';
import fileUploader from './components/fileUploader';
import {
  analysingDocumentsStatusText,
  APIMutationStatus,
  appRoutes,
  fileEnums,
  queryKeys
} from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';

interface CreateProjectProps {
  open: boolean;
  onClose: () => void;
}

export interface FileFormatSupprotType {
  drawings: boolean;
  specifications: boolean;
}

const StyledDatePicker = styled(DatePicker)`
  border-radius: 10px;
  padding: 8px 10px;
  width: 100%;
`;

const StyledDescription = styled(Input.TextArea)`
  resize: none !important;
`;

const StyledFlex = styled(Flex)`
  width: 100%;
`;

const StyledButton = styled(Button)`
  font-family: Roboto;
  font-weight: 400;
  font-size: 17px;
  border: 1px solid ${themeTokens.buttonBorder};
  padding: 16px 8px;
  border-radius: 4px;
`;

const StyledSaveButton = styled(Button)`
  font-family: Roboto;
  font-weight: 400;
  font-size: 17px;
  padding: 16px 10px;
  border-radius: 4px;
  background-color: ${themeTokens.buttonDark};
  color: ${themeTokens.textLight};
`;

const StyledInput = styled(Input)`
  border-radius: 4px;
  border: 1px solid ${themeTokens.inputBorder};
  padding: 8px 10px;
`;

const LoaderContainer = styled.div`
  width: 100%;
  height: 100%;
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
`;

const LoadingInfoTitle = styled.p<{ isTitleAvailable?: boolean }>`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: ${({ isTitleAvailable }) => (isTitleAvailable ? 500 : 400)};
  font-size: 26px;
  line-height: 40px;
  text-align: center;
`;

const LoadingInfoDescription = styled.p`
  color: ${themeTokens.textBlack};
  font-family: Inter;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
`;

const UploadFilesWrapper = styled(Col)`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;
const CreateProject: React.FC<CreateProjectProps> = ({ open, onClose }) => {
  const [drawingsFile, setDrawingsFile] = useState<Array<UploadFile>>([]);
  const [specificationsFile, setSpecificationsFile] = useState<Array<UploadFile>>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [fileFormatNotSupported, setFileFormatNotSupported] = useState<FileFormatSupprotType>({
    drawings: false,
    specifications: false
  });
  const [documentsUploadStatus, setDocumentsUploadStatus] = useState<APIMutationStatus>(
    APIMutationStatus.idle
  );
  const { notification } = App.useApp();
  const { useForm } = Form;
  const [form] = useForm();
  const formValues = Form.useWatch([], form);
  const isAnyFieldFilled = Object.values(formValues || {}).some(value => {
    return value !== undefined && value !== null && value !== '';
  });
  const { setSelectedVersion } = useGlobalStore();
  const fileUploadList = [
    {
      title: fileEnums.uploadDrawings,
      file: drawingsFile,
      isFilesFormatSupported: fileFormatNotSupported.drawings
    },
    {
      title: fileEnums.uploadSpecifications,
      file: specificationsFile,
      isFilesFormatSupported: fileFormatNotSupported.specifications
    }
  ];
  const hasAnyDocuments = useMemo(
    () => drawingsFile.length > 0 || specificationsFile.length > 0,
    [drawingsFile, specificationsFile]
  );

  const { data: usersList, isPending: isUsersListLoading } = useQuery({
    queryKey: [queryKeys.usersList],
    queryFn: () => userAPI.getAllUsers(0, 1000),
    select: res => res.data
  });

  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const uploadFileToS3 = async (file: File, presignedUrl: string): Promise<void> => {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to upload file: ${response.statusText}`);
    }
  };

  useEffect(() => {
    setFileFormatNotSupported({
      drawings: !drawingsFile.every(item => item.type === 'application/pdf'),
      specifications: !specificationsFile.every(item => item.type === 'application/pdf')
    });
  }, [drawingsFile, specificationsFile]);

  const findDocument = (fileId: string) => {
    const drawing = drawingsFile.find(file => file.uid === fileId);
    const specification = specificationsFile.find(file => file.uid === fileId);
    if (specification) return { file: specification, isDrawing: false };
    return { file: drawing, isDrawing: true };
  };

  const onProjectCreated = async (response: AxiosResponse<ProjectWithPresignedUrlsDTO, any>) => {
    if (hasAnyDocuments) {
      setDocumentsUploadStatus(APIMutationStatus.pending);
      const projectId = response.data.projectId;
      const documentSetVersionId = response.data.documentSetVersionId;
      const presignedUrls = response.data.responses;

      if (presignedUrls) {
        const fileUploadPromises = presignedUrls.map(async (item: PresignedUrlResponse) => {
          if (item.fileId && item.url && item.fileKey) {
            const documentInfo = findDocument(item.fileId);
            if (documentInfo.file) {
              const file = convertRcFileToFile(documentInfo.file);
              if (file) {
                await uploadFileToS3(file, item.url);
                const documentUploadConfirmDTO: DocumentUploadConfirmDTO = {
                  fileKey: item.fileKey,
                  size: file.size,
                  mimeType: file.type,
                  filePath: file.name,
                  inputDocumentType: documentInfo.isDrawing
                    ? DocumentUploadConfirmDTOInputDocumentTypeEnum.Drawing
                    : DocumentUploadConfirmDTOInputDocumentTypeEnum.Specification,
                  documentId: item.documentId
                };
                return documentUploadConfirmDTO;
              }
            }
          }
          return;
        });
        try {
          const result = await Promise.all(fileUploadPromises);
          setDocumentsUploadStatus(APIMutationStatus.success);
          const filteredResults = result.filter(item => item !== undefined);
          const results = filteredResults as Array<DocumentUploadConfirmDTO>;
          if (results.length > 0 && projectId && documentSetVersionId) {
            confirmDocumentsUpload({
              projectId,
              documentSetVersionId,
              documentUploadConfirmDTO: results
            });
          }
        } catch (error) {
          setDocumentsUploadStatus(APIMutationStatus.error);
          console.error('Error uploading file:', error);
        }
      }
    } else {
      setSelectedVersion(null);
      notification.success({ message: 'Project created successfully' });
      navigate(`${response.data.projectId}/${appRoutes.documents}`);
      handleModalClose();
    }
    queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
  };

  const {
    mutateAsync: mutate,
    status: createProjectStatus,
    reset,
    data: createProjectResponse
  } = useMutation({
    mutationFn: (args: { data: ProjectCreateDTO }) =>
      projectAPI.createProjectWithPresignedUrls(args.data),
    onSuccess: onProjectCreated,
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project creation failed') });
      reset();
    }
  });

  const {
    mutate: confirmDocumentsUpload,
    status: confirmDocumentsUploadStatus,
    reset: resetConfirmDocumentsUpdate
  } = useMutation({
    mutationFn: (args: {
      projectId: number;
      documentSetVersionId: number;
      documentUploadConfirmDTO: Array<DocumentUploadConfirmDTO>;
    }) =>
      projectAPI.confirmDocumentUpload(
        args.projectId,
        args.documentSetVersionId,
        args.documentUploadConfirmDTO
      )
  });

  const projectName = useMemo(
    () => form.getFieldValue('ProjectName') || '',
    [form, createProjectStatus]
  );

  const isTitleAvailable = useMemo(() => {
    return (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    );
  }, [confirmDocumentsUploadStatus, documentsUploadStatus]);

  const convertRcFileToFile = (rcFile: UploadFile): File | undefined => {
    const { name, lastModified, type, originFileObj } = rcFile;
    if (originFileObj)
      return new File([originFileObj], name, {
        lastModified,
        type
      });
    return undefined;
  };

  const createProject = async () => {
    const documents = [...drawingsFile, ...specificationsFile];
    const documentNames =
      documents.length > 0
        ? documents.map(document => ({ docId: document.uid, docName: document.name }))
        : [];
    const data: ProjectCreateDTO = {
      name: formValues['ProjectName'],
      location: formValues['Address'],
      endDate: dayjs(formValues['BidDueDate']).format(),
      documents: documentNames,
      owner: formValues['Owner'],
      bidCaptainId: formValues['BidCaptain'],
      documentSetVersion: formValues['DocumentVersion'],
      description: formValues['Description']
    };
    mutate({ data });
  };

  const resetModalState = () => {
    form.resetFields();
    setDrawingsFile([]);
    setSpecificationsFile([]);
    reset();
    resetConfirmDocumentsUpdate();
    setDocumentsUploadStatus(APIMutationStatus.idle);
  };

  const handleModalClose = () => {
    resetModalState();
    onClose();
  };

  const handleSave = async () => {
    if (createProjectStatus === APIMutationStatus.success) {
      handleModalClose();
      if (
        hasAnyDocuments &&
        documentsUploadStatus === APIMutationStatus.success &&
        confirmDocumentsUploadStatus === APIMutationStatus.success
      ) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      }
      return;
    }
    try {
      await form.validateFields();
      try {
        await createProject();
      } catch (error) {
        notification.error({ message: 'Project Creation failed' });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleCancel = () => {
    if (createProjectStatus === APIMutationStatus.error) {
      reset();
    } else {
      handleModalClose();
    }
  };

  const handleOnChange = (info: UploadChangeParam<UploadFile<any>>, name: string) => {
    setUploading(true);
    if (name === fileEnums.uploadDrawings) setDrawingsFile(info.fileList);
    else setSpecificationsFile(info.fileList);
    setUploading(false);
  };

  const handleClose = (e: React.MouseEvent, name: string, index: number) => {
    e.stopPropagation();
    if (name === fileEnums.uploadDrawings) {
      const drawings = [...drawingsFile];
      drawings.splice(index, 1);
      setDrawingsFile(drawings);
    } else if (name === fileEnums.uploadSpecifications) {
      const specifications = [...specificationsFile];
      specifications.splice(index, 1);
      setSpecificationsFile(specifications);
    }
  };

  const analysingDocumentsStatusInfo = useMemo(() => {
    if (confirmDocumentsUploadStatus === APIMutationStatus.pending)
      return {
        title: analysingDocumentsStatusText.documentsUploadingStatus,
        icon: <Spin size='large' />
      };
    if (documentsUploadStatus === APIMutationStatus.pending)
      return {
        title: analysingDocumentsStatusText.uploadingDocuments,
        icon: <Spin size='large' />
      };
    if (createProjectStatus === APIMutationStatus.pending)
      return {
        title: analysingDocumentsStatusText.creatingProject,
        icon: <Spin size='large' />
      };
    if (
      createProjectStatus === APIMutationStatus.error ||
      confirmDocumentsUploadStatus === APIMutationStatus.error ||
      documentsUploadStatus === APIMutationStatus.error
    )
      return {
        title: analysingDocumentsStatusText.somethingWentWrong,
        icon: <CustomIcon Icon={RxCrossCircled} size='70px' />
      };
    if (
      documentsUploadStatus === APIMutationStatus.success &&
      confirmDocumentsUploadStatus === APIMutationStatus.success
    )
      return {
        title: (
          <>
            {analysingDocumentsStatusText.ScopeBuilderAnalyzingDocuments} <br />
            {analysingDocumentsStatusText.ScopeBuilderAnalyzingDocumentsSubText}
          </>
        ),
        icon: <CustomIcon Icon={FaCircleCheck} size='70px' />
      };
    return { title: analysingDocumentsStatusText.documentsAnalysed, icon: <Spin size='large' /> };
  }, [createProjectStatus, documentsUploadStatus, confirmDocumentsUploadStatus]);

  const saveButtonText = useMemo(() => {
    if (createProjectStatus === APIMutationStatus.idle && hasAnyDocuments) return 'Save & Analyze';
    else if (createProjectStatus === APIMutationStatus.idle) return 'Save';
    // toDo add condition to retry failed uploads
    return 'Continue';
  }, [createProjectStatus, hasAnyDocuments]);

  const disabledDate: RangePickerProps['disabledDate'] = useMemo(
    () => (current: dayjs.Dayjs) => {
      return current && current < dayjs().startOf('day');
    },
    []
  );

  return (
    <Modal
      title={createProjectStatus === APIMutationStatus.idle ? 'Create New Project' : projectName}
      centered
      open={open}
      closable={false}
      maskClosable={false}
      onCancel={handleCancel}
      width='65%'
      footer={[
        <Flex justify='end' gap={8}>
          <StyledButton
            type='text'
            onClick={handleCancel}
            disabled={
              createProjectStatus === APIMutationStatus.pending ||
              createProjectStatus === APIMutationStatus.success
            }
          >
            Cancel
          </StyledButton>
          <StyledSaveButton
            loading={createProjectStatus === APIMutationStatus.pending}
            type='primary'
            onClick={handleSave}
            disabled={
              (createProjectStatus === APIMutationStatus.idle && !isAnyFieldFilled) ||
              createProjectStatus === APIMutationStatus.pending ||
              documentsUploadStatus === APIMutationStatus.pending ||
              confirmDocumentsUploadStatus === APIMutationStatus.pending ||
              createProjectStatus === APIMutationStatus.error ||
              fileFormatNotSupported.drawings ||
              fileFormatNotSupported.specifications
            }
          >
            {saveButtonText}
          </StyledSaveButton>
        </Flex>
      ]}
    >
      {createProjectStatus !== APIMutationStatus.idle && hasAnyDocuments ? (
        <LoaderContainer>
          {isTitleAvailable && (
            <LoadingInfoTitle isTitleAvailable={isTitleAvailable}>We’re on it!</LoadingInfoTitle>
          )}
          <LoadingInfoTitle isTitleAvailable={!isTitleAvailable}>
            {analysingDocumentsStatusInfo.title}
          </LoadingInfoTitle>
          {analysingDocumentsStatusInfo.icon}
          {(createProjectStatus === APIMutationStatus.pending ||
            documentsUploadStatus === APIMutationStatus.pending ||
            confirmDocumentsUploadStatus === APIMutationStatus.pending) && (
            <LoadingInfoDescription>Please wait</LoadingInfoDescription>
          )}
        </LoaderContainer>
      ) : (
        <Row gutter={[48, 24]}>
          <Col xs={24} lg={12}>
            <Form layout='vertical' form={form}>
              <Form.Item
                label='Project Name'
                name='ProjectName'
                rules={[{ required: true, message: 'Please enter project name' }]}
              >
                <StyledInput placeholder='Enter project name' />
              </Form.Item>
              <Form.Item
                label='Address'
                name='Address'
                rules={[{ required: true, message: 'Please enter project location' }]}
              >
                <StyledInput placeholder='Enter address' />
              </Form.Item>
              <Form.Item
                label='Owner'
                name='Owner'
                rules={[{ required: true, message: 'Please enter owner' }]}
              >
                <StyledInput placeholder='Enter owner name' />
              </Form.Item>
              <Form.Item label='Document Version' name='DocumentVersion'>
                <StyledInput placeholder='Enter document version' />
              </Form.Item>
              <Row gutter={16} justify='space-between'>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label='Bid Due Date'
                    name='BidDueDate'
                    extra='mm/dd/yy'
                    rules={[{ required: true, message: 'Please select project due date' }]}
                  >
                    <StyledDatePicker format='MM/DD/YY' disabledDate={disabledDate} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name='BidCaptain'
                    label='Bid Captain'
                    rules={[{ required: true, message: 'Please select bid captain' }]}
                  >
                    <Select
                      loading={isUsersListLoading}
                      disabled={isUsersListLoading}
                      size='large'
                      style={{ width: '100%' }}
                      placeholder='Select bid captain'
                    >
                      {usersList?.content?.map(user => (
                        <Select.Option key={user.id} value={user.id}>
                          {user?.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label='Description' name='Description'>
                <StyledDescription placeholder='Enter project description' />
              </Form.Item>
            </Form>
          </Col>
          <UploadFilesWrapper xs={24} lg={12}>
            {fileUploadList.map(({ title, file, isFilesFormatSupported }) =>
              fileUploader(
                title,
                uploading,
                file,
                handleClose,
                handleOnChange,
                isFilesFormatSupported
              )
            )}
          </UploadFilesWrapper>
        </Row>
      )}
    </Modal>
  );
};

export default CreateProject;
