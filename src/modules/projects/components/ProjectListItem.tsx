import {
  EyeOutlined,
  DeleteOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { Card, Divider, Flex, Tag, Tooltip } from 'antd';
import { useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ProjectDTO } from 'src/api';
import HasProjectPermission from 'src/modules/guards/HasProjectPermission';
import { DocProcessingJobStatus, appRoutes } from 'src/modules/utils/constant';
import { UserProjectPermission } from 'src/modules/utils/permissions';
import useGlobalStore from 'src/store/useGlobalStore';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';

const StyledCard = styled(Card)`
  width: 100%;
  position: relative;
  height: 100%;

  .action-icons {
    display: none;
    position: absolute;
    right: 24px;
  }

  &:hover .action-icons {
    display: flex;
  }

  .project-status {
    position: absolute;
    right: 24px;
    top: 10px;
  }
`;

const ActionIcon = styled.span`
  font-size: 18px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
`;

const ProjectTitle = styled.div`
  font-family: 'Inter';
  font-weight: 500;
  font-size: 27px;
  line-height: 28px;
`;

const ProjectLabel = styled.span`
  font-weight: 500;
  font-size: 16px;
`;

const ProjectDescription = styled.span`
  font-weight: 600;
  font-size: 16px;
`;

const LoaderContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ProjectName = styled(Link)`
  font-weight: 500;
  color: ${themeTokens.linkBlue};
  text-decoration: none;

  &:hover {
    color: ${themeTokens.linkBlue};
    text-decoration: underline;
  }
`;

const ProjectDetails = styled.div`
  font-weight: 400;
  font-size: 16px;
  line-height: 18px;
`;

const Description = styled.span<{ width?: number }>`
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
`;

enum ProjectDisplayStatus {
  processing = 'Processing',
  processed = 'Processed',
  error = 'Error'
}

const ProjectListItem: React.FC<{
  project: ProjectDTO;
  onProjectClickHandler: (id?: number) => void;
  handleDeleteProject: (id?: number) => void;
}> = ({ project, onProjectClickHandler, handleDeleteProject }) => {
  const navigate = useNavigate();
  const { documentProcessingListeners } = useGlobalStore();
  const status = useMemo(() => {
    if (documentProcessingListeners && project.projectId) {
      return documentProcessingListeners[project.projectId];
    }
    return null;
  }, [documentProcessingListeners, project.projectId]);

  const projectDocumentsStatus = useMemo(() => {
    switch (status) {
      case DocProcessingJobStatus.inProgress:
      case DocProcessingJobStatus.queued:
        return {
          status: ProjectDisplayStatus.processing,
          icon: <SyncOutlined spin />,
          color: 'processing'
        };
      case DocProcessingJobStatus.failed:
        return {
          status: ProjectDisplayStatus.error,
          icon: <CloseCircleOutlined />,
          color: 'error'
        };
      case DocProcessingJobStatus.completed:
        return {
          status: ProjectDisplayStatus.processed,
          icon: <CheckCircleOutlined />,
          color: 'success'
        };
      default:
        return null;
    }
  }, [status]);

  return (
    <StyledCard>
      <Flex vertical>
        <Flex gap={15} vertical>
          <ProjectTitle>
            <ProjectName
              to={
                (project?.scopeCount ? project?.scopeCount : 0) > 0
                  ? `${project?.projectId}/${appRoutes.scopes}`
                  : `${project?.projectId}/${appRoutes.documents}`
              }
              onClick={() => onProjectClickHandler(project?.projectId)}
            >
              {project?.name}
            </ProjectName>
          </ProjectTitle>
          <Flex gap={10} vertical>
            <ProjectDetails>
              <ProjectLabel>Address:</ProjectLabel>
              &nbsp;{project?.location}
            </ProjectDetails>
            <ProjectDetails>
              <ProjectLabel>Owner Contact Info:</ProjectLabel>
              &nbsp;{project?.owner}
            </ProjectDetails>
            <LoaderContainer>
              <ProjectLabel>Documents status:</ProjectLabel>
              <Tag icon={projectDocumentsStatus?.icon} color={projectDocumentsStatus?.color}>
                {projectDocumentsStatus?.status}
              </Tag>
            </LoaderContainer>
          </Flex>
        </Flex>
        <Divider />
        <ProjectDetails>
          <Description>
            <ProjectDescription>Description:</ProjectDescription>
            &nbsp;{project?.description || '-'}
          </Description>
        </ProjectDetails>
        <Flex gap={16} align='center' className='action-icons'>
          <Tooltip title='View Project'>
            <ActionIcon
              onClick={() => {
                onProjectClickHandler(project?.projectId);
                navigate(`${project?.projectId}`);
              }}
            >
              <EyeOutlined />
            </ActionIcon>
          </Tooltip>
          <HasProjectPermission requiredPermissions={[UserProjectPermission.DELETE_PROJECT]}>
            <Tooltip title='Delete Project'>
              <ActionIcon onClick={() => handleDeleteProject(project?.projectId)}>
                <DeleteOutlined />
              </ActionIcon>
            </Tooltip>
          </HasProjectPermission>
        </Flex>
      </Flex>
    </StyledCard>
  );
};

export default ProjectListItem;
