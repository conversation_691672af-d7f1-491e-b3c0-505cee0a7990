import { CheckOutlined, CloseOutlined, PaperClipOutlined, UploadOutlined } from '@ant-design/icons';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { Flex, Spin } from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import Dragger from 'antd/es/upload/Dragger';
import styled from 'styled-components';
import uploadIcon from '../../../assets/images/uploadIcon.svg';

const FlexContainer = styled.div`
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  border-radius: 20px;
  overflow: auto;
  margin: 2px;
`;

const StyledSelectFileText = styled.p`
  color: #1976b9;
  font-size: 14px;
  font-family: Roboto;
  font-weight: 500;
  padding: 4px 2px;
`;

const StyledSelectFileIcon = styled(UploadOutlined)`
  color: #1976b9;
  padding: 4px 8px;
`;

const StyledSelectFileContainer = styled.div`
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  border-radius: 20px;
  border: solid #d9d9d9 2px;
  padding: 0px 8px 0px 2px;
`;

const StyledDragDropText = styled.p`
  font-family: inter;
  font-size: 14px;
  font-weight: 400;
  color: #505050;
`;

const StyledOrText = styled.p`
  font-family: Roboto;
  font-size: 12px;
  font-weight: 500;
  color: #9b9b9b;
`;

const StyledcheckOutlined = styled(CheckOutlined)`
  color: #6dd92a;
  font-size: 20px;
  padding-bottom: 12px;
`;

const StyledFileUploadedText = styled.p`
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  color: #3d3d3d;
  padding-bottom: 8px;
`;

const StyledPaperClip = styled(PaperClipOutlined)`
  color: #363636;
  background-color: #f5f5f5;
  padding: 4px 0px 4px 15px;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 400;
`;

const StyledFileName = styled.p`
  color: #363636;
  background-color: #f5f5f5;
  padding: 4px 5px;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 400;
`;

const StyledCloseOutlined = styled(CloseOutlined)`
  color: #e85959;
  background-color: #fee6e6;
  padding: 4px 10px 4px 6px;
  font-family: Roboto;
  font-size: 10px;
  font-weight: 400;
`;

const StyledTitle = styled.div`
  font-family: inter;
  color: #565656;
  font-weight: 400;
  font-size: 16px;
  line-height: 23.44px;
`;

const StyledDragger = styled(Dragger)`
  .ant-upload-drag {
    border-radius: 4px;
    height: 170px;
    background-color: #ffffff;
    border: none;
  }
`;

const StyledContainer = styled.div`
  border: 1px dashed #c7c7c7;
  border-radius: 4px;
`;

const ImageAndTextWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
`;

const fileUploader = (
  title: string,
  uploading: boolean,
  files: Array<UploadFile>,
  handleClose: (e: React.MouseEvent, name: string, index: number) => void,
  onChange: (info: UploadChangeParam<UploadFile<any>>, fieldName: string) => void
) => {
  return (
    <Flex vertical={true} key={title} gap={8}>
      <StyledTitle>{title}</StyledTitle>
      <StyledContainer>
        <StyledDragger
          name={title}
          multiple={true}
          onChange={info => onChange(info, title)}
          showUploadList={false}
          fileList={files}
          beforeUpload={() => false}
          accept='.pdf'
        >
          {uploading && files.length !== 0 && (
            <Spin indicator={<LoadingOutlined spin />} size='small' />
          )}
          {!uploading && files.length !== 0 ? (
            <>
              <StyledcheckOutlined />
              <StyledFileUploadedText>Files Uploaded</StyledFileUploadedText>
              {files.map((file, index) => (
                <FlexContainer>
                  <StyledPaperClip />
                  <StyledFileName>{file.name}</StyledFileName>
                  <StyledCloseOutlined onClick={e => handleClose(e, title, index)} />
                </FlexContainer>
              ))}
            </>
          ) : (
            files.length === 0 && (
              <ImageAndTextWrapper>
                <img src={uploadIcon} alt='upload icon' />
                <StyledDragDropText>
                  Click to upload or drag & drop your file here
                </StyledDragDropText>
              </ImageAndTextWrapper>
            )
          )}
        </StyledDragger>
      </StyledContainer>
    </Flex>
  );
};

export default fileUploader;
