import { SearchOutlined } from '@ant-design/icons';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Button,
  Dropdown,
  Empty,
  Flex,
  Form,
  Input,
  List,
  Pagination,
  Select,
  Tooltip,
  App
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { FaPlus } from 'react-icons/fa6';
import { LiaSlidersHSolid } from 'react-icons/lia';
import { LuArrowDownUp } from 'react-icons/lu';
import { useNavigate } from 'react-router-dom';
import { GetAllProjectsStatusEnum, PageProjectDTO, ProjectDTO } from 'src/api';
import { companyAPI, projectAPI } from 'src/api/apiClient';
import { themeTokens } from 'src/theme/tokens';
import styled from 'styled-components';
import CreateProject from './createProject';
import useGlobalStore from '../../store/useGlobalStore';
import CustomIcon from '../common/customIcon';
import HasPermission from '../guards/HasPermission';
import { queryKeys } from '../utils/constant';
import { extractErrorMessage } from '../utils/errorHandler';
import { UserPermission } from '../utils/permissions';
import ProjectListItem from './components/ProjectListItem';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 24px;
  font-family: 'Inter';
`;

const Content = styled.div`
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 48px;
`;

const Header = styled.div`
  font-weight: 400;
  font-size: 40px;
  line-height: 28px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 24px 0;
  background: ${themeTokens.pageBg};
`;

const StyledButton = styled(Button)`
  font-size: 20px;
  line-height: 28px;
`;

const StyledInput = styled(Input)`
  width: 300px;
  border: 1px solid #656565;
  border-radius: 10px;
`;

const FilterDropdownContainer = styled.div`
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
`;

interface FilterState {
  companyId?: number;
  location?: string;
  status?: GetAllProjectsStatusEnum;
}

const ProjectsList: React.FC = () => {
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState<boolean>(false);
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [filters, setFilters] = useState<FilterState>({});
  const [isSortAscending, setIsSortAscending] = useState<boolean>(false);

  const [filterForm] = Form.useForm();
  const { setSelectedProjectId, currentUser, setSelectedVersion, selectedProjectId } =
    useGlobalStore();
  const queryClient = useQueryClient();
  const { modal, notification } = App.useApp();
  const navigate = useNavigate();

  // Debounce search text to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // API Queries
  const { data: companiesList, isPending: isCompaniesLoading } = useQuery({
    queryKey: [queryKeys.companiesList],
    queryFn: () => companyAPI.getAllCompanies(0, 1000),
    select: res => res.data.content,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const { data: projectsData, isPending } = useQuery({
    queryKey: [
      queryKeys.allProjects,
      currentPage,
      pageSize,
      debouncedSearchText,
      filters.companyId,
      filters.location,
      filters.status,
      isSortAscending
    ],
    queryFn: () =>
      projectAPI.getAllProjects(
        filters.companyId,
        debouncedSearchText,
        undefined,
        filters.location,
        filters.status,
        undefined,
        currentPage,
        pageSize,
        ['createdAt', isSortAscending ? 'asc' : 'desc']
      ),
    select: res => res.data as PageProjectDTO
  });

  const handleCreateProject = useCallback(() => setIsCreateProjectModalOpen(true), []);
  const onCreateProjectModalClose = useCallback(() => setIsCreateProjectModalOpen(false), []);

  const onProjectClickHandler = useCallback(
    (projectId?: number) => {
      if (projectId) {
        if (projectId.toString() !== selectedProjectId) setSelectedVersion(null);
        setSelectedProjectId(projectId.toString());
      }
    },
    [setSelectedProjectId, setSelectedVersion, selectedProjectId]
  );

  // Delete project mutation
  const { mutate: deleteProject } = useMutation({
    mutationFn: (projectId: number) => projectAPI.softDeleteProject(projectId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.allProjects] });
      notification.success({ message: 'Project deleted successfully' });
    },
    onError: error => {
      notification.error({ message: extractErrorMessage(error, 'Project deletion Failed') });
    }
  });

  // Handle delete project
  const handleDeleteProject = useCallback(
    (projectId?: number) => {
      if (!projectId) return;

      modal.confirm({
        title: 'Delete Project',
        content: 'Are you sure you want to delete this project?',
        okText: 'Yes, Delete',
        okType: 'danger',
        cancelText: 'Cancel',
        onOk: () => {
          deleteProject(projectId);
        }
      });
    },
    [deleteProject, modal]
  );

  const handleFilterReset = useCallback(() => {
    filterForm.resetFields();
    setFilters({});
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const handleFilterApply = useCallback(() => {
    const values = filterForm.getFieldsValue();
    setFilters(values);
    setCurrentPage(0);
    setIsFilterDropdownOpen(false);
  }, [filterForm]);

  const onSearchHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    setCurrentPage(0);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page - 1);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  const FilterFormContent = () => (
    <Form form={filterForm} layout='vertical' initialValues={filters}>
      {!currentUser?.companyId ? (
        <Form.Item label='Company' name='companyId'>
          <Select
            placeholder='Select Company'
            allowClear
            loading={isCompaniesLoading}
            options={(companiesList || [])?.map(company => ({
              label: company.name,
              value: company.id
            }))}
          />
        </Form.Item>
      ) : (
        <></>
      )}
      <Form.Item label='Location' name='location'>
        <Input placeholder='Enter location' />
      </Form.Item>
      <Form.Item label='Status' name='status'>
        <Select
          placeholder='Select Status'
          allowClear
          options={[
            { label: 'Active', value: 'ACTIVE' },
            { label: 'Inactive', value: 'INACTIVE' },
            { label: 'Completed', value: 'COMPLETED' },
            { label: 'Pending', value: 'PENDING' }
          ]}
        />
      </Form.Item>
      <Flex justify='space-between' align='center'>
        <Button onClick={handleFilterReset}>Reset</Button>
        <Button type='primary' onClick={handleFilterApply}>
          Apply Filters
        </Button>
      </Flex>
    </Form>
  );

  return (
    <Container>
      <Content>
        <Flex vertical>
          <HeaderContainer>
            <Header>Projects</Header>
            <Flex gap={24} align='center'>
              <StyledInput
                size='large'
                placeholder='Search here'
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={onSearchHandler}
              />
              <Tooltip title='Sort'>
                <LuArrowDownUp
                  cursor='pointer'
                  onClick={() => setIsSortAscending(!isSortAscending)}
                  size={20}
                />
              </Tooltip>
              <Tooltip title='Filter'>
                <Dropdown
                  trigger={['click']}
                  placement='bottomRight'
                  open={isFilterDropdownOpen}
                  onOpenChange={setIsFilterDropdownOpen}
                  dropdownRender={() => (
                    <FilterDropdownContainer>
                      <Flex vertical gap={16}>
                        <h3>Filter Projects</h3>
                        <FilterFormContent />
                      </Flex>
                    </FilterDropdownContainer>
                  )}
                >
                  <LiaSlidersHSolid cursor='pointer' size={22} />
                </Dropdown>
              </Tooltip>
              <HasPermission requiredPermissions={[UserPermission.CREATE_PROJECT]}>
                <StyledButton
                  onClick={handleCreateProject}
                  icon={<CustomIcon Icon={FaPlus} />}
                  type='primary'
                >
                  Add Project
                </StyledButton>
              </HasPermission>
            </Flex>
          </HeaderContainer>
          <List
            grid={{
              gutter: 25,
              xs: 1,
              sm: 1,
              md: 2,
              lg: 2,
              xl: 2,
              xxl: 2
            }}
            loading={isPending}
            dataSource={(projectsData?.content || []) as ProjectDTO[]}
            locale={{ emptyText: <Empty description='No projects found' /> }}
            renderItem={item => (
              <List.Item>
                <ProjectListItem
                  project={item}
                  onProjectClickHandler={onProjectClickHandler}
                  handleDeleteProject={handleDeleteProject}
                />
              </List.Item>
            )}
          />
          {projectsData && projectsData.totalElements && projectsData.totalElements > 0 ? (
            <Flex justify='center' align='center'>
              <Pagination
                current={currentPage + 1}
                pageSize={pageSize}
                total={projectsData.totalElements}
                showSizeChanger
                pageSizeOptions={['5', '10', '20', '50']}
                onChange={handlePageChange}
                showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} projects`}
              />
            </Flex>
          ) : null}
        </Flex>
      </Content>
      {isCreateProjectModalOpen && (
        <CreateProject open={isCreateProjectModalOpen} onClose={onCreateProjectModalClose} />
      )}
    </Container>
  );
};

export default ProjectsList;
