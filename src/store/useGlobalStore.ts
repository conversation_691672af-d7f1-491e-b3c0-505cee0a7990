import { UserDTO } from 'src/api';
import { create } from 'zustand';
import { DocProcessingJobStatus, StorageKey } from '../modules/utils/constant';
import { getItem, setItem } from '../modules/utils/storage';

interface GlobalState {
  selectedProjectId: string | null;
  setSelectedProjectId: (id: string | null) => void;
  isScopeSubMenuOpen: boolean;
  setScopeSubMenuOpen: (isOpen: boolean) => void;
  isAuthenticated: boolean | null;
  setIsAuthenticated: (authStatus: boolean) => void;
  isSideMenuOpen: boolean;
  setIsSideMenuOpen: (isOpen: boolean) => void;
  currentUser: UserDTO | null;
  setCurrentUser: (user: UserDTO | null) => void;
  userProjectPermissions: string[];
  setUserProjectPermissions: (permissions: string[]) => void;
  documentProcessingListeners: Record<number, DocProcessingJobStatus> | null;
  updateDocumentProcessingListeners: (record: Record<number, DocProcessingJobStatus>) => void;
  selectedProjectName: string | null;
  setSelectedProjectName: (name: string | null) => void;
  selectedVersion: Record<string, string | number> | null;
  setSelectedVersion: (
    versionInfo: { version: string | null; versionId: number | null } | null
  ) => void;
}

const useGlobalStore = create<GlobalState>(set => ({
  selectedProjectId: getItem(StorageKey.selectedProjectId) || null,
  setSelectedProjectId: id => {
    set({ selectedProjectId: id });
    setItem(StorageKey.selectedProjectId, id);
  },
  isScopeSubMenuOpen: true,
  setScopeSubMenuOpen: isOpen => set({ isScopeSubMenuOpen: isOpen }),
  isAuthenticated: getItem(StorageKey.isAuthenticated) || null,
  setIsAuthenticated: authStatus => {
    set({ isAuthenticated: authStatus });
    setItem(StorageKey.isAuthenticated, authStatus);
  },
  isSideMenuOpen: false,
  setIsSideMenuOpen: isOpen => set({ isSideMenuOpen: isOpen }),
  currentUser: null,
  setCurrentUser: user => set({ currentUser: user }),
  userProjectPermissions: [],
  setUserProjectPermissions: permissions => set({ userProjectPermissions: permissions }),
  selectedProjectName: null,
  setSelectedProjectName: name => set({ selectedProjectName: name }),
  selectedVersion: getItem(StorageKey.versionInfo) || null,
  setSelectedVersion: versionInfo => {
    const versionObject: Record<string, string | number> = {};
    if (versionInfo?.version) versionObject.version = versionInfo.version;
    if (versionInfo?.versionId) versionObject.versionId = versionInfo.versionId;
    set({
      selectedVersion: Object.keys(versionObject).length > 0 ? versionObject : null
    });
    setItem(StorageKey.versionInfo, Object.keys(versionObject).length > 0 ? versionObject : null);
  },
  documentProcessingListeners: null,
  updateDocumentProcessingListeners: record =>
    set(state =>
      state.documentProcessingListeners
        ? {
            documentProcessingListeners: { ...state.documentProcessingListeners, ...record }
          }
        : { documentProcessingListeners: record }
    )
}));

export default useGlobalStore;
