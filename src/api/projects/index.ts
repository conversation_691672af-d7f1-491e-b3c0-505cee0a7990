import { ProjectData, ProjectResponse, ProjectsResponse } from './model';

const projectsData = [
  {
    projectId: 1,
    projectName: 'Project1',
    address: 'address1',
    ownerContact: '<EMAIL>',
    description: 'Description of Project1'
  },
  {
    projectId: 2,
    projectName: 'Project2',
    address: 'address2',
    ownerContact: '<EMAIL>',
    description: 'Description of Project2'
  },
  {
    projectId: 3,
    projectName: 'Project3',
    address: 'address3',
    ownerContact: '<EMAIL>',
    description: 'Description of Project3'
  },
  {
    projectId: 4,
    projectName: 'Project4',
    address: 'address4',
    ownerContact: '<EMAIL>',
    description: 'Description of Project4'
  }
];

const getAllProjects = async (): Promise<ProjectsResponse> => {
  return Promise.resolve({
    data: projectsData
  });
};

const getProjectById = async (projectId: number): Promise<ProjectResponse> => {
  const project = projectsData.find(project => project.projectId === projectId);

  if (!project) {
    throw new Error('Project not found');
  }

  const bidDueDate = new Date().toLocaleDateString('en-US');
  const projectData: ProjectData = {
    ...project,
    bidDueDate: bidDueDate,
    projectType: 'Project Type',
    scopes: 5,
    drawings: ['drawing1.pdf', 'drawing2.pdf'],
    specifications: ['specification1.pdf', 'specification2.pdf']
  };

  return Promise.resolve({ data: projectData });
};

export { getAllProjects, getProjectById };
