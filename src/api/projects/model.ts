type ProjectsData = {
  projectId: number;
  projectName: string;
  address: string;
  ownerContact: string;
  description: string;
};

type ProjectData = {
  projectId: number;
  projectName: string;
  address: string;
  ownerContact: string;
  description: string;
  bidDueDate: string;
  projectType: string;
  scopes: number;
  drawings: string[];
  specifications: string[];
};

type ProjectsResponse = {
  data: ProjectsData[];
};

type ProjectResponse = {
  data: ProjectData;
};

export type { ProjectsData, ProjectData, ProjectsResponse, ProjectResponse };
