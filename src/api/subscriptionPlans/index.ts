import { SubscriptionPlansResponse } from './model';

const subscriptionPlansData = [
  { planId: 1, planName: 'Basic Plan' },
  { planId: 2, planName: 'Standard Plan' },
  { planId: 3, planName: 'Premium Plan' }
];

const getSubscriptionPlansList = async (): Promise<SubscriptionPlansResponse> => {
  return Promise.resolve({
    data: subscriptionPlansData
  });
};

export { getSubscriptionPlansList };
