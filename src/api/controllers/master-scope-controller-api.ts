/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ImportMasterScopesFromExcelRequest } from '../models';
// @ts-ignore
import type { MasterScopeDTO } from '../models';
// @ts-ignore
import type { PageMasterScopeDTO } from '../models';
/**
 * MasterScopeControllerApi - axios parameter creator
 * @export
 */
export const MasterScopeControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createMasterScope: async (masterScopeDTO: MasterScopeDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'masterScopeDTO' is not null or undefined
            assertParamExists('createMasterScope', 'masterScopeDTO', masterScopeDTO)
            const localVarPath = `/api/master-scopes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(masterScopeDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteMasterScope: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteMasterScope', 'id', id)
            const localVarPath = `/api/master-scopes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getMasterScopeById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getMasterScopeById', 'id', id)
            const localVarPath = `/api/master-scopes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [id] 
         * @param {GetMasterScopesDisciplineEnum} [discipline] 
         * @param {string} [name] 
         * @param {string} [division] 
         * @param {string} [keyword] 
         * @param {string} [scopeSubSystem] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMasterScopes: async (id?: number, discipline?: GetMasterScopesDisciplineEnum, name?: string, division?: string, keyword?: string, scopeSubSystem?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/master-scopes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (discipline !== undefined) {
                localVarQueryParameter['discipline'] = discipline;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (division !== undefined) {
                localVarQueryParameter['division'] = division;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (scopeSubSystem !== undefined) {
                localVarQueryParameter['scopeSubSystem'] = scopeSubSystem;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {ImportMasterScopesFromExcelRequest} [importMasterScopesFromExcelRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importMasterScopesFromExcel: async (importMasterScopesFromExcelRequest?: ImportMasterScopesFromExcelRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/master-scopes/import/excel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(importMasterScopesFromExcelRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateMasterScope: async (id: number, masterScopeDTO: MasterScopeDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateMasterScope', 'id', id)
            // verify required parameter 'masterScopeDTO' is not null or undefined
            assertParamExists('updateMasterScope', 'masterScopeDTO', masterScopeDTO)
            const localVarPath = `/api/master-scopes/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(masterScopeDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MasterScopeControllerApi - functional programming interface
 * @export
 */
export const MasterScopeControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = MasterScopeControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createMasterScope(masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createMasterScope(masterScopeDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.createMasterScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteMasterScope(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteMasterScope(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.deleteMasterScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getMasterScopeById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getMasterScopeById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.getMasterScopeById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [id] 
         * @param {GetMasterScopesDisciplineEnum} [discipline] 
         * @param {string} [name] 
         * @param {string} [division] 
         * @param {string} [keyword] 
         * @param {string} [scopeSubSystem] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getMasterScopes(id?: number, discipline?: GetMasterScopesDisciplineEnum, name?: string, division?: string, keyword?: string, scopeSubSystem?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageMasterScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getMasterScopes(id, discipline, name, division, keyword, scopeSubSystem, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.getMasterScopes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {ImportMasterScopesFromExcelRequest} [importMasterScopesFromExcelRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importMasterScopesFromExcel(importMasterScopesFromExcelRequest?: ImportMasterScopesFromExcelRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importMasterScopesFromExcel(importMasterScopesFromExcelRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.importMasterScopesFromExcel']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateMasterScope(id: number, masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterScopeDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateMasterScope(id, masterScopeDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterScopeControllerApi.updateMasterScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * MasterScopeControllerApi - factory interface
 * @export
 */
export const MasterScopeControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = MasterScopeControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createMasterScope(masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO> {
            return localVarFp.createMasterScope(masterScopeDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteMasterScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteMasterScope(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getMasterScopeById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO> {
            return localVarFp.getMasterScopeById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [id] 
         * @param {GetMasterScopesDisciplineEnum} [discipline] 
         * @param {string} [name] 
         * @param {string} [division] 
         * @param {string} [keyword] 
         * @param {string} [scopeSubSystem] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMasterScopes(id?: number, discipline?: GetMasterScopesDisciplineEnum, name?: string, division?: string, keyword?: string, scopeSubSystem?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageMasterScopeDTO> {
            return localVarFp.getMasterScopes(id, discipline, name, division, keyword, scopeSubSystem, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {ImportMasterScopesFromExcelRequest} [importMasterScopesFromExcelRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importMasterScopesFromExcel(importMasterScopesFromExcelRequest?: ImportMasterScopesFromExcelRequest, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.importMasterScopesFromExcel(importMasterScopesFromExcelRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {MasterScopeDTO} masterScopeDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateMasterScope(id: number, masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO> {
            return localVarFp.updateMasterScope(id, masterScopeDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MasterScopeControllerApi - interface
 * @export
 * @interface MasterScopeControllerApi
 */
export interface MasterScopeControllerApiInterface {
    /**
     * 
     * @param {MasterScopeDTO} masterScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    createMasterScope(masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    deleteMasterScope(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    getMasterScopeById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO>;

    /**
     * 
     * @param {number} [id] 
     * @param {GetMasterScopesDisciplineEnum} [discipline] 
     * @param {string} [name] 
     * @param {string} [division] 
     * @param {string} [keyword] 
     * @param {string} [scopeSubSystem] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    getMasterScopes(id?: number, discipline?: GetMasterScopesDisciplineEnum, name?: string, division?: string, keyword?: string, scopeSubSystem?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageMasterScopeDTO>;

    /**
     * 
     * @param {ImportMasterScopesFromExcelRequest} [importMasterScopesFromExcelRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    importMasterScopesFromExcel(importMasterScopesFromExcelRequest?: ImportMasterScopesFromExcelRequest, options?: RawAxiosRequestConfig): AxiosPromise<object>;

    /**
     * 
     * @param {number} id 
     * @param {MasterScopeDTO} masterScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApiInterface
     */
    updateMasterScope(id: number, masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig): AxiosPromise<MasterScopeDTO>;

}

/**
 * MasterScopeControllerApi - object-oriented interface
 * @export
 * @class MasterScopeControllerApi
 * @extends {BaseAPI}
 */
export class MasterScopeControllerApi extends BaseAPI implements MasterScopeControllerApiInterface {
    /**
     * 
     * @param {MasterScopeDTO} masterScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public createMasterScope(masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).createMasterScope(masterScopeDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public deleteMasterScope(id: number, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).deleteMasterScope(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public getMasterScopeById(id: number, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).getMasterScopeById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [id] 
     * @param {GetMasterScopesDisciplineEnum} [discipline] 
     * @param {string} [name] 
     * @param {string} [division] 
     * @param {string} [keyword] 
     * @param {string} [scopeSubSystem] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public getMasterScopes(id?: number, discipline?: GetMasterScopesDisciplineEnum, name?: string, division?: string, keyword?: string, scopeSubSystem?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).getMasterScopes(id, discipline, name, division, keyword, scopeSubSystem, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {ImportMasterScopesFromExcelRequest} [importMasterScopesFromExcelRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public importMasterScopesFromExcel(importMasterScopesFromExcelRequest?: ImportMasterScopesFromExcelRequest, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).importMasterScopesFromExcel(importMasterScopesFromExcelRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {MasterScopeDTO} masterScopeDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterScopeControllerApi
     */
    public updateMasterScope(id: number, masterScopeDTO: MasterScopeDTO, options?: RawAxiosRequestConfig) {
        return MasterScopeControllerApiFp(this.configuration).updateMasterScope(id, masterScopeDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetMasterScopesDisciplineEnum = {
    All: 'ALL',
    ProcurementAndContractingRequirements: 'PROCUREMENT_AND_CONTRACTING_REQUIREMENTS',
    GeneralRequirements: 'GENERAL_REQUIREMENTS',
    Architectural: 'ARCHITECTURAL',
    Structural: 'STRUCTURAL',
    FireProtection: 'FIRE_PROTECTION',
    Plumbing: 'PLUMBING',
    Mechanical: 'MECHANICAL',
    IntegratedAutomation: 'INTEGRATED_AUTOMATION',
    Electrical: 'ELECTRICAL',
    Communications: 'COMMUNICATIONS',
    ElectronicSafetyAndSecurity: 'ELECTRONIC_SAFETY_AND_SECURITY',
    Site: 'SITE',
    Landscape: 'LANDSCAPE',
    Transportation: 'TRANSPORTATION',
    WaterwayMarineAndCoastal: 'WATERWAY_MARINE_AND_COASTAL',
    Process: 'PROCESS',
    MaterialHandling: 'MATERIAL_HANDLING',
    ProcessHeating: 'PROCESS_HEATING',
    ProcessGasAndLiquidHandlingPurificationAndStorageEquipment: 'PROCESS_GAS_AND_LIQUID_HANDLING_PURIFICATION_AND_STORAGE_EQUIPMENT',
    PollutionAndWasteControlEquipment: 'POLLUTION_AND_WASTE_CONTROL_EQUIPMENT',
    IndustrySpecificManufacturingEquipment: 'INDUSTRY_SPECIFIC_MANUFACTURING_EQUIPMENT',
    WaterAndWastewaterEquipment: 'WATER_AND_WASTEWATER_EQUIPMENT',
    ElectricalPowerGeneration: 'ELECTRICAL_POWER_GENERATION',
    Site2: 'Site',
    Landscape2: 'Landscape',
    Architectural2: 'Architectural',
    Structural2: 'Structural',
    Mechanical2: 'Mechanical',
    FireProtection2: 'FireProtection',
    FireAlarm: 'FireAlarm',
    Plumbing2: 'Plumbing',
    Electrical2: 'Electrical',
    Civil: 'Civil'
} as const;
export type GetMasterScopesDisciplineEnum = typeof GetMasterScopesDisciplineEnum[keyof typeof GetMasterScopesDisciplineEnum];
