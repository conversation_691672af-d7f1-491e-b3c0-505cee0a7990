/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { SheetDTO } from '../models';
/**
 * SheetControllerApi - axios parameter creator
 * @export
 */
export const SheetControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSheet: async (sheetDTO: SheetDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sheetDTO' is not null or undefined
            assertParamExists('createSheet', 'sheetDTO', sheetDTO)
            const localVarPath = `/api/sheets`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(sheetDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSheet: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteSheet', 'id', id)
            const localVarPath = `/api/sheets/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getSheetById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getSheetById', 'id', id)
            const localVarPath = `/api/sheets/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {string} [title] 
         * @param {string} [sheetNumber] 
         * @param {string} [discipline] 
         * @param {number} [documentId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSheets: async (id?: number, projectId?: number, title?: string, sheetNumber?: string, discipline?: string, documentId?: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sheets`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (projectId !== undefined) {
                localVarQueryParameter['projectId'] = projectId;
            }

            if (title !== undefined) {
                localVarQueryParameter['title'] = title;
            }

            if (sheetNumber !== undefined) {
                localVarQueryParameter['sheetNumber'] = sheetNumber;
            }

            if (discipline !== undefined) {
                localVarQueryParameter['discipline'] = discipline;
            }

            if (documentId !== undefined) {
                localVarQueryParameter['documentId'] = documentId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getSheetsByProjectId: async (projectId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getSheetsByProjectId', 'projectId', projectId)
            const localVarPath = `/api/sheets/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSheet: async (id: number, sheetDTO: SheetDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateSheet', 'id', id)
            // verify required parameter 'sheetDTO' is not null or undefined
            assertParamExists('updateSheet', 'sheetDTO', sheetDTO)
            const localVarPath = `/api/sheets/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(sheetDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SheetControllerApi - functional programming interface
 * @export
 */
export const SheetControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SheetControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSheet(sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSheet(sheetDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.createSheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSheet(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSheet(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.deleteSheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getSheetById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSheetById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.getSheetById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {string} [title] 
         * @param {string} [sheetNumber] 
         * @param {string} [discipline] 
         * @param {number} [documentId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSheets(id?: number, projectId?: number, title?: string, sheetNumber?: string, discipline?: string, documentId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSheets(id, projectId, title, sheetNumber, discipline, documentId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.getSheets']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getSheetsByProjectId(projectId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<SheetDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSheetsByProjectId(projectId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.getSheetsByProjectId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSheet(id: number, sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSheet(id, sheetDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SheetControllerApi.updateSheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SheetControllerApi - factory interface
 * @export
 */
export const SheetControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SheetControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSheet(sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO> {
            return localVarFp.createSheet(sheetDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSheet(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteSheet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getSheetById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO> {
            return localVarFp.getSheetById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [id] 
         * @param {number} [projectId] 
         * @param {string} [title] 
         * @param {string} [sheetNumber] 
         * @param {string} [discipline] 
         * @param {number} [documentId] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSheets(id?: number, projectId?: number, title?: string, sheetNumber?: string, discipline?: string, documentId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.getSheets(id, projectId, title, sheetNumber, discipline, documentId, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getSheetsByProjectId(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<SheetDTO>> {
            return localVarFp.getSheetsByProjectId(projectId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {SheetDTO} sheetDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSheet(id: number, sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO> {
            return localVarFp.updateSheet(id, sheetDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SheetControllerApi - interface
 * @export
 * @interface SheetControllerApi
 */
export interface SheetControllerApiInterface {
    /**
     * 
     * @param {SheetDTO} sheetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    createSheet(sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    deleteSheet(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    getSheetById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO>;

    /**
     * 
     * @param {number} [id] 
     * @param {number} [projectId] 
     * @param {string} [title] 
     * @param {string} [sheetNumber] 
     * @param {string} [discipline] 
     * @param {number} [documentId] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    getSheets(id?: number, projectId?: number, title?: string, sheetNumber?: string, discipline?: string, documentId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<object>;

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    getSheetsByProjectId(projectId: number, options?: RawAxiosRequestConfig): AxiosPromise<Array<SheetDTO>>;

    /**
     * 
     * @param {number} id 
     * @param {SheetDTO} sheetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApiInterface
     */
    updateSheet(id: number, sheetDTO: SheetDTO, options?: RawAxiosRequestConfig): AxiosPromise<SheetDTO>;

}

/**
 * SheetControllerApi - object-oriented interface
 * @export
 * @class SheetControllerApi
 * @extends {BaseAPI}
 */
export class SheetControllerApi extends BaseAPI implements SheetControllerApiInterface {
    /**
     * 
     * @param {SheetDTO} sheetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public createSheet(sheetDTO: SheetDTO, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).createSheet(sheetDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public deleteSheet(id: number, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).deleteSheet(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public getSheetById(id: number, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).getSheetById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [id] 
     * @param {number} [projectId] 
     * @param {string} [title] 
     * @param {string} [sheetNumber] 
     * @param {string} [discipline] 
     * @param {number} [documentId] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public getSheets(id?: number, projectId?: number, title?: string, sheetNumber?: string, discipline?: string, documentId?: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).getSheets(id, projectId, title, sheetNumber, discipline, documentId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public getSheetsByProjectId(projectId: number, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).getSheetsByProjectId(projectId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {SheetDTO} sheetDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SheetControllerApi
     */
    public updateSheet(id: number, sheetDTO: SheetDTO, options?: RawAxiosRequestConfig) {
        return SheetControllerApiFp(this.configuration).updateSheet(id, sheetDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

