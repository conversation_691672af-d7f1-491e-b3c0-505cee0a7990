/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { BidSheetCreateDTO } from '../models';
// @ts-ignore
import type { BidSheetDTO } from '../models';
// @ts-ignore
import type { PageBidSheetDTO } from '../models';
/**
 * BidSheetControllerApi - axios parameter creator
 * @export
 */
export const BidSheetControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {BidSheetCreateDTO} bidSheetCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBidSheet: async (bidSheetCreateDTO: BidSheetCreateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bidSheetCreateDTO' is not null or undefined
            assertParamExists('createBidSheet', 'bidSheetCreateDTO', bidSheetCreateDTO)
            const localVarPath = `/api/bidsheets`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(bidSheetCreateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} bidSheetId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBidSheet: async (bidSheetId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bidSheetId' is not null or undefined
            assertParamExists('deleteBidSheet', 'bidSheetId', bidSheetId)
            const localVarPath = `/api/bidsheets/{bidSheetId}`
                .replace(`{${"bidSheetId"}}`, encodeURIComponent(String(bidSheetId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidSheetsByProject: async (projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectId' is not null or undefined
            assertParamExists('getBidSheetsByProject', 'projectId', projectId)
            const localVarPath = `/api/bidsheets/project/{projectId}`
                .replace(`{${"projectId"}}`, encodeURIComponent(String(projectId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidSheetsByScope: async (scopeId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('getBidSheetsByScope', 'scopeId', scopeId)
            const localVarPath = `/api/bidsheets/scope/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * BidSheetControllerApi - functional programming interface
 * @export
 */
export const BidSheetControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = BidSheetControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {BidSheetCreateDTO} bidSheetCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createBidSheet(bidSheetCreateDTO: BidSheetCreateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BidSheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createBidSheet(bidSheetCreateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidSheetControllerApi.createBidSheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} bidSheetId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteBidSheet(bidSheetId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteBidSheet(bidSheetId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidSheetControllerApi.deleteBidSheet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBidSheetsByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageBidSheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBidSheetsByProject(projectId, name, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidSheetControllerApi.getBidSheetsByProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBidSheetsByScope(scopeId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BidSheetDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBidSheetsByScope(scopeId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidSheetControllerApi.getBidSheetsByScope']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * BidSheetControllerApi - factory interface
 * @export
 */
export const BidSheetControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = BidSheetControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {BidSheetCreateDTO} bidSheetCreateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBidSheet(bidSheetCreateDTO: BidSheetCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidSheetDTO> {
            return localVarFp.createBidSheet(bidSheetCreateDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} bidSheetId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBidSheet(bidSheetId: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteBidSheet(bidSheetId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} projectId 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidSheetsByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageBidSheetDTO> {
            return localVarFp.getBidSheetsByProject(projectId, name, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidSheetsByScope(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<BidSheetDTO> {
            return localVarFp.getBidSheetsByScope(scopeId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * BidSheetControllerApi - interface
 * @export
 * @interface BidSheetControllerApi
 */
export interface BidSheetControllerApiInterface {
    /**
     * 
     * @param {BidSheetCreateDTO} bidSheetCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApiInterface
     */
    createBidSheet(bidSheetCreateDTO: BidSheetCreateDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidSheetDTO>;

    /**
     * 
     * @param {number} bidSheetId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApiInterface
     */
    deleteBidSheet(bidSheetId: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} projectId 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApiInterface
     */
    getBidSheetsByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageBidSheetDTO>;

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApiInterface
     */
    getBidSheetsByScope(scopeId: number, options?: RawAxiosRequestConfig): AxiosPromise<BidSheetDTO>;

}

/**
 * BidSheetControllerApi - object-oriented interface
 * @export
 * @class BidSheetControllerApi
 * @extends {BaseAPI}
 */
export class BidSheetControllerApi extends BaseAPI implements BidSheetControllerApiInterface {
    /**
     * 
     * @param {BidSheetCreateDTO} bidSheetCreateDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApi
     */
    public createBidSheet(bidSheetCreateDTO: BidSheetCreateDTO, options?: RawAxiosRequestConfig) {
        return BidSheetControllerApiFp(this.configuration).createBidSheet(bidSheetCreateDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} bidSheetId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApi
     */
    public deleteBidSheet(bidSheetId: number, options?: RawAxiosRequestConfig) {
        return BidSheetControllerApiFp(this.configuration).deleteBidSheet(bidSheetId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} projectId 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApi
     */
    public getBidSheetsByProject(projectId: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return BidSheetControllerApiFp(this.configuration).getBidSheetsByProject(projectId, name, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidSheetControllerApi
     */
    public getBidSheetsByScope(scopeId: number, options?: RawAxiosRequestConfig) {
        return BidSheetControllerApiFp(this.configuration).getBidSheetsByScope(scopeId, options).then((request) => request(this.axios, this.basePath));
    }
}

