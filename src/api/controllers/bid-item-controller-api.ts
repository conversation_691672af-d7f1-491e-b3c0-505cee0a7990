/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { BidItemDTO } from '../models';
// @ts-ignore
import type { PageBidItemDTO } from '../models';
/**
 * BidItemControllerApi - axios parameter creator
 * @export
 */
export const BidItemControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} bidItemId 
         * @param {number} sheetId 
         * @param {string} [notes] 
         * @param {string} [status] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addSheetToBidItem: async (bidItemId: number, sheetId: number, notes?: string, status?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bidItemId' is not null or undefined
            assertParamExists('addSheetToBidItem', 'bidItemId', bidItemId)
            // verify required parameter 'sheetId' is not null or undefined
            assertParamExists('addSheetToBidItem', 'sheetId', sheetId)
            const localVarPath = `/api/bid-items/{bidItemId}/sheets/{sheetId}`
                .replace(`{${"bidItemId"}}`, encodeURIComponent(String(bidItemId)))
                .replace(`{${"sheetId"}}`, encodeURIComponent(String(sheetId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (notes !== undefined) {
                localVarQueryParameter['notes'] = notes;
            }

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBidItem: async (bidItemDTO: BidItemDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bidItemDTO' is not null or undefined
            assertParamExists('createBidItem', 'bidItemDTO', bidItemDTO)
            const localVarPath = `/api/bid-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(bidItemDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBidItem: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteBidItem', 'id', id)
            const localVarPath = `/api/bid-items/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getAllBidItems: async (page?: number, size?: number, sort?: Array<string>, name?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/bid-items/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItemById: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getBidItemById', 'id', id)
            const localVarPath = `/api/bid-items/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [scopeId] 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItems: async (scopeId?: number, name?: string, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/bid-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (scopeId !== undefined) {
                localVarQueryParameter['scopeId'] = scopeId;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItemsByScopeId: async (scopeId: number, page?: number, size?: number, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'scopeId' is not null or undefined
            assertParamExists('getBidItemsByScopeId', 'scopeId', scopeId)
            const localVarPath = `/api/bid-items/scope/{scopeId}`
                .replace(`{${"scopeId"}}`, encodeURIComponent(String(scopeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateBidItem: async (id: number, bidItemDTO: BidItemDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateBidItem', 'id', id)
            // verify required parameter 'bidItemDTO' is not null or undefined
            assertParamExists('updateBidItem', 'bidItemDTO', bidItemDTO)
            const localVarPath = `/api/bid-items/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication JSESSIONID required


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(bidItemDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * BidItemControllerApi - functional programming interface
 * @export
 */
export const BidItemControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = BidItemControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} bidItemId 
         * @param {number} sheetId 
         * @param {string} [notes] 
         * @param {string} [status] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addSheetToBidItem(bidItemId: number, sheetId: number, notes?: string, status?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addSheetToBidItem(bidItemId, sheetId, notes, status, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.addSheetToBidItem']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createBidItem(bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BidItemDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createBidItem(bidItemDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.createBidItem']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteBidItem(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteBidItem(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.deleteBidItem']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getAllBidItems(page?: number, size?: number, sort?: Array<string>, name?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageBidItemDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllBidItems(page, size, sort, name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.getAllBidItems']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBidItemById(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BidItemDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBidItemById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.getBidItemById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [scopeId] 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBidItems(scopeId?: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBidItems(scopeId, name, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.getBidItems']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBidItemsByScopeId(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PageBidItemDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBidItemsByScopeId(scopeId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.getBidItemsByScopeId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateBidItem(id: number, bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BidItemDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateBidItem(id, bidItemDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BidItemControllerApi.updateBidItem']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * BidItemControllerApi - factory interface
 * @export
 */
export const BidItemControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = BidItemControllerApiFp(configuration)
    return {
        /**
         * 
         * @param {number} bidItemId 
         * @param {number} sheetId 
         * @param {string} [notes] 
         * @param {string} [status] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addSheetToBidItem(bidItemId: number, sheetId: number, notes?: string, status?: string, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.addSheetToBidItem(bidItemId, sheetId, notes, status, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBidItem(bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO> {
            return localVarFp.createBidItem(bidItemDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBidItem(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.deleteBidItem(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getAllBidItems(page?: number, size?: number, sort?: Array<string>, name?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageBidItemDTO> {
            return localVarFp.getAllBidItems(page, size, sort, name, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItemById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO> {
            return localVarFp.getBidItemById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [scopeId] 
         * @param {string} [name] 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItems(scopeId?: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.getBidItems(scopeId, name, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} scopeId 
         * @param {number} [page] 
         * @param {number} [size] 
         * @param {Array<string>} [sort] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBidItemsByScopeId(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageBidItemDTO> {
            return localVarFp.getBidItemsByScopeId(scopeId, page, size, sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {BidItemDTO} bidItemDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateBidItem(id: number, bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO> {
            return localVarFp.updateBidItem(id, bidItemDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * BidItemControllerApi - interface
 * @export
 * @interface BidItemControllerApi
 */
export interface BidItemControllerApiInterface {
    /**
     * 
     * @param {number} bidItemId 
     * @param {number} sheetId 
     * @param {string} [notes] 
     * @param {string} [status] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    addSheetToBidItem(bidItemId: number, sheetId: number, notes?: string, status?: string, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {BidItemDTO} bidItemDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    createBidItem(bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    deleteBidItem(id: number, options?: RawAxiosRequestConfig): AxiosPromise<string>;

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    getAllBidItems(page?: number, size?: number, sort?: Array<string>, name?: string, options?: RawAxiosRequestConfig): AxiosPromise<PageBidItemDTO>;

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    getBidItemById(id: number, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO>;

    /**
     * 
     * @param {number} [scopeId] 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    getBidItems(scopeId?: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<object>;

    /**
     * 
     * @param {number} scopeId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    getBidItemsByScopeId(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<PageBidItemDTO>;

    /**
     * 
     * @param {number} id 
     * @param {BidItemDTO} bidItemDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApiInterface
     */
    updateBidItem(id: number, bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig): AxiosPromise<BidItemDTO>;

}

/**
 * BidItemControllerApi - object-oriented interface
 * @export
 * @class BidItemControllerApi
 * @extends {BaseAPI}
 */
export class BidItemControllerApi extends BaseAPI implements BidItemControllerApiInterface {
    /**
     * 
     * @param {number} bidItemId 
     * @param {number} sheetId 
     * @param {string} [notes] 
     * @param {string} [status] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public addSheetToBidItem(bidItemId: number, sheetId: number, notes?: string, status?: string, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).addSheetToBidItem(bidItemId, sheetId, notes, status, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {BidItemDTO} bidItemDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public createBidItem(bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).createBidItem(bidItemDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public deleteBidItem(id: number, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).deleteBidItem(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {string} [name] 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public getAllBidItems(page?: number, size?: number, sort?: Array<string>, name?: string, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).getAllBidItems(page, size, sort, name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public getBidItemById(id: number, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).getBidItemById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [scopeId] 
     * @param {string} [name] 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public getBidItems(scopeId?: number, name?: string, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).getBidItems(scopeId, name, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} scopeId 
     * @param {number} [page] 
     * @param {number} [size] 
     * @param {Array<string>} [sort] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public getBidItemsByScopeId(scopeId: number, page?: number, size?: number, sort?: Array<string>, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).getBidItemsByScopeId(scopeId, page, size, sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {BidItemDTO} bidItemDTO 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BidItemControllerApi
     */
    public updateBidItem(id: number, bidItemDTO: BidItemDTO, options?: RawAxiosRequestConfig) {
        return BidItemControllerApiFp(this.configuration).updateBidItem(id, bidItemDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

