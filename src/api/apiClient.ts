import apiConfig from './apiConfig';
import { DocumentControllerApi } from './controllers/document-controller-api';
import {
  BidItemControllerApi,
  CompanyControllerApi,
  CompanySubscriptionControllerApi,
  Configuration,
  MasterScopeControllerApi,
  NoteControllerApi,
  ProjectControllerApi,
  ProjectSpecificationControllerApi,
  ProjectUserControllerApi,
  RoleControllerApi,
  ScopeControllerApi,
  SheetControllerApi,
  SubscriptionPlanControllerApi,
  UserControllerApi
} from './index';

const config = new Configuration({
  baseOptions: apiConfig.defaults
});

const baseUrl = apiConfig.defaults.baseURL;

const projectAPI = new ProjectControllerApi(config, baseUrl, apiConfig);
const scopeAPI = new ScopeControllerApi(config, baseUrl, apiConfig);
const bidItemAPI = new BidItemControllerApi(config, baseUrl, apiConfig);
const sheetAPI = new SheetControllerApi(config, baseUrl, apiConfig);
const userAPI = new UserControllerApi(config, baseUrl, apiConfig);
const roleAPI = new RoleControllerApi(config, baseUrl, apiConfig);
const companyAPI = new CompanyControllerApi(config, baseUrl, apiConfig);
const companySubscriptionAPI = new CompanySubscriptionControllerApi(config, baseUrl, apiConfig);
const subscriptionPlanApi = new SubscriptionPlanControllerApi(config, baseUrl, apiConfig);
const projectUserAPI = new ProjectUserControllerApi(config, baseUrl, apiConfig);
const masterScopeAPI = new MasterScopeControllerApi(config, baseUrl, apiConfig);
const documentAPI = new DocumentControllerApi(config, baseUrl, apiConfig);
const noteAPI = new NoteControllerApi(config, baseUrl, apiConfig);
const specificationAPI = new ProjectSpecificationControllerApi(config, baseUrl, apiConfig);

export {
  projectAPI,
  scopeAPI,
  bidItemAPI,
  sheetAPI,
  userAPI,
  roleAPI,
  companyAPI,
  companySubscriptionAPI,
  subscriptionPlanApi,
  projectUserAPI,
  masterScopeAPI,
  documentAPI,
  noteAPI,
  specificationAPI
};
