type ScopesData = { scopeId: string; key: string; label: string };

type BidItem = {
  bidId: string;
  bidItem: string;
  sheets: string;
  specifications: string;
  notes: string;
};

type ScopeData = {
  id: string;
  name: string;
  bidItems: BidItem[];
  documents: any;
};

type ScopeImageData = { name: string; url: string };

type ScopesResponse = {
  data: ScopesData[];
};

type ScopeResponse = {
  data: ScopeData;
};

type ScopeImagesResponse = {
  data: ScopeImageData[];
};

export type {
  ScopesData,
  ScopesResponse,
  ScopeData,
  ScopeResponse,
  BidItem,
  ScopeImageData,
  ScopeImagesResponse
};
