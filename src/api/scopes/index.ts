import { SheetDTO } from '../models';
import { ScopeResponse, ScopeImagesResponse, ScopesResponse } from './model';

const scopes = [
  { scopeId: '1', key: '1', label: 'Concrete' },
  { scopeId: '2', key: '2', label: 'Carpet' },
  { scopeId: '3', key: '3', label: 'Electrical' },
  { scopeId: '4', key: '4', label: 'Plumbing' },
  { scopeId: '5', key: '5', label: 'Tile' }
];

const getAllScopes = async (projectId: string): Promise<ScopesResponse> => {
  return Promise.resolve({
    data: scopes
  });
};

const getAllSheets = async (): Promise<{ data: SheetDTO[] }> => {
  return Promise.resolve({
    data: [
      {
        id: 1,
        projectId: 1,
        sheetNumber: '1',
        title: 's11',
        documentId: 1,
        discipline: 'concrete',
        pageNumber: 1
      },
      {
        id: 2,
        projectId: 1,
        sheetNumber: '1',
        title: 's22',
        documentId: 1,
        discipline: 'concrete',
        pageNumber: 1
      },
      {
        id: 3,
        projectId: 1,
        sheetNumber: '1',
        title: 's33',
        documentId: 1,
        discipline: 'concrete',
        pageNumber: 1
      }
    ]
  });
};

const getAllBidItems = async (scopeId: string): Promise<any> => {
  return Promise.resolve({
    data: [
      {
        bidId: '1',
        bidItem: 'Provide 6” SOG',
        sheets: 'S1.11, S2.11',
        specifications: '033030 Concrete, 034044 Concrete',
        notes: ''
      },
      {
        bidId: '2',
        bidItem: 'Provide 24” Footing',
        sheets: 'S2.22, S3.33',
        specifications: '033031 Footings',
        notes:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'
      },
      {
        bidId: '3',
        bidItem: 'Provide 15” Footing',
        sheets: 'S4.44, S5.55',
        specifications: '033167 Footings',
        notes: ''
      }
    ]
  });
};

const getScopeById = async (scopeId: string): Promise<ScopeResponse> => {
  const scopeName = scopes.find(scope => scope.scopeId === scopeId)?.label;

  return Promise.resolve({
    data: {
      id: scopeId,
      name: scopeName || 'Scope Name',
      bidItems: [
        {
          bidId: '1',
          bidItem: 'Provide 6” SOG',
          sheets: 'S1.11, S2.11',
          specifications: '033030 Concrete, 034044 Concrete',
          notes: ''
        },
        {
          bidId: '2',
          bidItem: 'Provide 24” Footing',
          sheets: 'S2.22, S3.33',
          specifications: '033031 Footings',
          notes:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'
        },
        {
          bidId: '3',
          bidItem: 'Provide 15” Footing',
          sheets: 'S4.44, S5.55',
          specifications: '033167 Footings',
          notes: ''
        }
      ]
    }
  });
};

export { scopes, getAllScopes, getAllBidItems, getScopeById, getAllSheets };
