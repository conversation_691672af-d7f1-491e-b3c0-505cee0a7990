/* tslint:disable */
/* eslint-disable */
/**
 * WyreAI API
 * API documentation for Scopebuilder Application
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface DocumentWithPresignedUrlDTO
 */
export interface DocumentWithPresignedUrlDTO {
    /**
     * 
     * @type {number}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'title'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'fileType'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'filePath'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'presignedUrl'?: string;
    /**
     * 
     * @type {number}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'size'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'mimeType'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'inputDocumentType'?: DocumentWithPresignedUrlDTOInputDocumentTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {number}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'createdBy'?: number;
    /**
     * 
     * @type {number}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'modifiedBy'?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentWithPresignedUrlDTO
     */
    'createdByName'?: string;
}

export const DocumentWithPresignedUrlDTOInputDocumentTypeEnum = {
    Drawing: 'DRAWING',
    Specification: 'SPECIFICATION'
} as const;

export type DocumentWithPresignedUrlDTOInputDocumentTypeEnum = typeof DocumentWithPresignedUrlDTOInputDocumentTypeEnum[keyof typeof DocumentWithPresignedUrlDTOInputDocumentTypeEnum];


